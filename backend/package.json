{"name": "gameflex-cdk-backend", "version": "1.0.0", "description": "GameFlex Backend using AWS CDK for infrastructure and deployment", "main": "bin/cdk.js", "bin": {"cdk": "bin/cdk.js"}, "scripts": {"start": "./start.sh", "stop": "./stop.sh", "install-deps": "./scripts/install-dependencies.sh", "build": "tsc", "watch": "tsc -w", "deploy": "./deploy.sh", "deploy:dev": "./deploy.sh development", "deploy:staging": "./deploy.sh staging", "deploy:production": "./deploy.sh production", "synth": "cdk synth", "diff": "cdk diff", "destroy": "cdk destroy", "cdk": "cdk", "test": "jest --config jest.config.js", "test:verbose": "VERBOSE_TESTS=true jest --config jest.config.js", "test:watch": "jest --config jest.config.js --watch", "test:coverage": "jest --config jest.config.js --coverage", "test:auth": "jest --config jest.config.js tests/auth.test.ts", "test:auth:verbose": "VERBOSE_TESTS=true jest --config jest.config.js tests/auth.test.ts", "test:posts": "jest --config jest.config.js tests/posts.test.ts", "test:posts:verbose": "VERBOSE_TESTS=true jest --config jest.config.js tests/posts.test.ts", "test:old": "jest tests-old", "test:run": "./scripts/run-tests.sh"}, "keywords": ["aws", "cdk", "serverless", "lambda", "api-gateway", "dynamodb", "cognito", "cloudflare-r2", "gameflex"], "author": "GameFlex Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@types/aws-lambda": "^8.10.150", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/node": "22.7.9", "@types/node-jose": "^1.1.13", "aws-cdk": "2.1022.0", "axios": "^1.6.0", "babel-jest": "^29.7.0", "dotenv": "^17.2.0", "esbuild": "^0.25.8", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.848.0", "@aws-sdk/client-dynamodb": "^3.848.0", "@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/client-secrets-manager": "^3.848.0", "@aws-sdk/lib-dynamodb": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@types/bad-words": "^3.0.3", "aws-cdk-lib": "2.206.0", "aws-sdk": "^2.1499.0", "bad-words": "^4.0.0", "constructs": "^10.0.0", "node-jose": "^2.2.0", "uuid": "^9.0.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/gameflex/gameflex-backend"}, "bugs": {"url": "https://github.com/gameflex/gameflex-backend/issues"}, "homepage": "https://github.com/gameflex/gameflex-backend#readme"}