import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// TypeScript interfaces
interface R2Config {
    accountId?: string;
    accessKeyId?: string;
    secretAccessKey?: string;
    endpoint?: string;
    bucketName?: string;
    publicUrl?: string;
}

interface AppConfig {
    cloudflareApiToken?: string;
    testUserEmail?: string;
    testUserPassword?: string;
    debugMode?: string;
    apiBaseUrl?: string;
    userPoolId?: string;
    userPoolClientId?: string;
}

interface MockR2Client {
    getSignedUrl: (operation: string, params: any) => string;
    deleteObject: () => { promise: () => Promise<void> };
    putObject: () => { promise: () => Promise<void> };
    getObject: () => { promise: () => Promise<{ Body: Buffer }> };
}

// Cache for R2 configuration to avoid repeated Secrets Manager calls
let r2ConfigCache: R2Config | null = null;
let cacheTimestamp: number | null = null;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get R2 configuration from AWS Secrets Manager (all environments)
 */
async function getR2Config(): Promise<R2Config> {
    const environment = process.env.ENVIRONMENT || 'development';
    const secretName = process.env.R2_SECRET_NAME;

    if (!secretName) {
        // Fallback to environment variables for backward compatibility
        console.warn('R2_SECRET_NAME not found, falling back to environment variables');
        return {
            accountId: process.env.R2_ACCOUNT_ID,
            accessKeyId: process.env.R2_ACCESS_KEY_ID,
            secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
            endpoint: process.env.R2_ENDPOINT,
            bucketName: process.env.R2_BUCKET_NAME,
            publicUrl: process.env.R2_PUBLIC_URL
        };
    }

    // Use AWS Secrets Manager for all environments

    // Check cache first
    const now = Date.now();
    if (r2ConfigCache && cacheTimestamp && (now - cacheTimestamp) < CACHE_TTL) {
        return r2ConfigCache;
    }

    try {
        const secretsManager = new SecretsManagerClient({
            region: process.env.AWS_REGION || 'us-west-2'
        });

        const command = new GetSecretValueCommand({
            SecretId: secretName
        });

        const result = await secretsManager.send(command);

        if (!result.SecretString) {
            throw new Error('Secret value is empty');
        }

        const config: R2Config = JSON.parse(result.SecretString);

        // Cache the configuration
        r2ConfigCache = config;
        cacheTimestamp = now;

        return config;
    } catch (error) {
        console.error('Failed to retrieve R2 configuration from Secrets Manager:', error);
        throw new Error('Failed to retrieve R2 configuration');
    }
}

/**
 * Create R2 client with proper configuration
 */
async function createR2Client(): Promise<S3Client | MockR2Client> {
    const config = await getR2Config();

    // Return mock client if configuration is incomplete (for development)
    if (!config.endpoint || !config.accessKeyId || !config.secretAccessKey) {
        console.warn('R2 configuration incomplete, returning mock client');
        return {
            getSignedUrl: (operation: string, params: any) => {
                return `https://mock-r2-url.com/${params.Bucket}/${params.Key}?operation=${operation}`;
            },
            deleteObject: () => ({ promise: () => Promise.resolve() }),
            putObject: () => ({ promise: () => Promise.resolve() }),
            getObject: () => ({ promise: () => Promise.resolve({ Body: Buffer.from('mock') }) })
        };
    }

    const r2Config = {
        endpoint: config.endpoint,
        credentials: {
            accessKeyId: config.accessKeyId,
            secretAccessKey: config.secretAccessKey
        },
        region: 'auto', // R2 uses 'auto' as region
        forcePathStyle: true
    };

    return new S3Client(r2Config);
}

/**
 * Get R2 bucket name from configuration
 */
async function getR2BucketName(): Promise<string> {
    const config = await getR2Config();
    return config.bucketName || process.env.R2_BUCKET_NAME || 'gameflex-development';
}

/**
 * Get R2 public URL from configuration
 */
async function getR2PublicUrl(): Promise<string> {
    const config = await getR2Config();
    return config.publicUrl || process.env.R2_PUBLIC_URL || 'https://pub-34709f09e8384ef1a67928492571c01d.r2.dev';
}

/**
 * Get application configuration from AWS Secrets Manager
 */
async function getAppConfig(): Promise<AppConfig> {
    const secretName = process.env.APP_CONFIG_SECRET_NAME;

    if (!secretName) {
        // Fallback to environment variables
        console.warn('APP_CONFIG_SECRET_NAME not found, falling back to environment variables');
        return {
            cloudflareApiToken: process.env.CLOUDFLARE_API_TOKEN,
            testUserEmail: process.env.TEST_USER_EMAIL,
            testUserPassword: process.env.TEST_USER_PASSWORD,
            debugMode: process.env.DEBUG_MODE || process.env.ENVIRONMENT,
            apiBaseUrl: process.env.API_BASE_URL,
            userPoolId: process.env.USER_POOL_ID,
            userPoolClientId: process.env.USER_POOL_CLIENT_ID
        };
    }

    try {
        const secretsManager = new SecretsManagerClient({
            region: process.env.AWS_REGION || 'us-west-2'
        });

        const command = new GetSecretValueCommand({
            SecretId: secretName
        });

        const result = await secretsManager.send(command);

        if (!result.SecretString) {
            throw new Error('Secret value is empty');
        }

        return JSON.parse(result.SecretString);
    } catch (error) {
        console.error('Failed to retrieve app configuration from Secrets Manager:', error);
        throw new Error('Failed to retrieve app configuration');
    }
}

export {
    getR2Config,
    createR2Client,
    getR2BucketName,
    getR2PublicUrl,
    getAppConfig
};

export type {
    R2Config,
    AppConfig
};
