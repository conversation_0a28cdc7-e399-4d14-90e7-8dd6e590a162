{"name": "gameflex-media-lambda", "version": "1.0.0", "description": "GameFlex Media Lambda Functions", "main": "dist/index.js", "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.658.1", "@aws-sdk/client-s3": "^3.658.1", "@aws-sdk/client-secrets-manager": "^3.658.1", "@aws-sdk/lib-dynamodb": "^3.658.1", "uuid": "^10.0.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.145", "@types/node": "^22.7.4", "@types/uuid": "^10.0.0", "typescript": "^5.6.2"}}