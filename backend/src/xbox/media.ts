import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand } from '@aws-sdk/lib-dynamodb';
import { createResponse } from '../utils/response';
import { getUserIdFromContext } from '../utils/auth';
import axios from 'axios';

// AWS Configuration
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

const USERS_TABLE = process.env.USERS_TABLE;

// Xbox Live API endpoints
const XBOX_API_BASE = 'https://xboxlive.com';
const ACTIVITY_HISTORY_ENDPOINT = 'https://activityhub.xboxlive.com/users/xuid({xuid})/activity/history';

// TypeScript interfaces
interface XboxMediaItem {
    id: string;
    type: 'screenshot' | 'gameclip';
    title: string;
    thumbnailUrl: string;
    downloadUrl: string;
    gameTitle: string;
    dateTaken: string;
    fileSize: number;
    duration?: number; // For game clips
    resolutionWidth: number;
    resolutionHeight: number;
    platform: string;
    titleId: string;
}

interface XboxApiResponse {
    numItems: number;
    activityItems: any[];
    contToken?: string;
    pollingToken?: string;
}

// Get Xbox screenshots for authenticated user
const getXboxScreenshots = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get user's Xbox credentials
        const xboxCredentials = await getUserXboxCredentials(userId);
        if (!xboxCredentials) {
            return createResponse(404, { error: 'No Xbox account linked or token expired' });
        }

        // Parse query parameters
        const queryParams = event.queryStringParameters || {};
        const numItems = parseInt(queryParams.numItems || '25');
        const contToken = queryParams.contToken;

        // Fetch screenshots from Xbox Live API
        const screenshots = await fetchXboxMedia(
            xboxCredentials,
            'Screenshot',
            { numItems, contToken }
        );

        return createResponse(200, {
            screenshots: screenshots.items,
            pagination: {
                hasMore: !!screenshots.contToken,
                contToken: screenshots.contToken
            }
        });

    } catch (error) {
        console.error('GetXboxScreenshots error:', error);
        return createResponse(500, { 
            error: 'Failed to fetch Xbox screenshots', 
            details: (error as Error).message 
        });
    }
};

// Get Xbox game clips for authenticated user
const getXboxGameClips = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get user's Xbox credentials
        const xboxCredentials = await getUserXboxCredentials(userId);
        if (!xboxCredentials) {
            return createResponse(404, { error: 'No Xbox account linked or token expired' });
        }

        // Parse query parameters
        const queryParams = event.queryStringParameters || {};
        const numItems = parseInt(queryParams.numItems || '25');
        const contToken = queryParams.contToken;

        // Fetch game clips from Xbox Live API
        const gameClips = await fetchXboxMedia(
            xboxCredentials,
            'GameDVR',
            { numItems, contToken }
        );

        return createResponse(200, {
            gameClips: gameClips.items,
            pagination: {
                hasMore: !!gameClips.contToken,
                contToken: gameClips.contToken
            }
        });

    } catch (error) {
        console.error('GetXboxGameClips error:', error);
        return createResponse(500, { 
            error: 'Failed to fetch Xbox game clips', 
            details: (error as Error).message 
        });
    }
};

// Download Xbox media item
const downloadXboxMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const mediaId = event.pathParameters?.mediaId;
        if (!mediaId) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        // Get user's Xbox credentials
        const xboxCredentials = await getUserXboxCredentials(userId);
        if (!xboxCredentials) {
            return createResponse(404, { error: 'No Xbox account linked or token expired' });
        }

        // For now, return the download URL - in a real implementation,
        // you might want to proxy the download or store it in your own storage
        return createResponse(200, {
            message: 'Media download URL retrieved',
            downloadUrl: `https://xboxlive.com/media/${mediaId}/download`,
            mediaId
        });

    } catch (error) {
        console.error('DownloadXboxMedia error:', error);
        return createResponse(500, { 
            error: 'Failed to download Xbox media', 
            details: (error as Error).message 
        });
    }
};

// Helper function to get user's Xbox credentials
const getUserXboxCredentials = async (userId: string) => {
    try {
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return null;
        }

        const user = userResult.Item;

        if (!user.xboxUserId || !user.xboxXstsToken || !user.xboxUserHash) {
            return null;
        }

        // Check if token is still valid
        const tokenExpiry = new Date(user.xboxTokenExpiry);
        const now = new Date();
        if (tokenExpiry <= now) {
            return null; // Token expired
        }

        return {
            xuid: user.xboxUserId,
            xstsToken: user.xboxXstsToken,
            userHash: user.xboxUserHash,
            gamertag: user.xboxGamertag
        };

    } catch (error) {
        console.error('Error getting Xbox credentials:', error);
        return null;
    }
};

// Helper function to fetch media from Xbox Live API
const fetchXboxMedia = async (
    credentials: any,
    activityType: 'Screenshot' | 'GameDVR',
    options: { numItems: number; contToken?: string }
): Promise<{ items: XboxMediaItem[]; contToken?: string }> => {
    try {
        const url = ACTIVITY_HISTORY_ENDPOINT.replace('{xuid}', credentials.xuid);
        
        const params: any = {
            numItems: options.numItems,
            activityTypes: activityType
        };

        if (options.contToken) {
            params.contToken = options.contToken;
        }

        const headers = {
            'Authorization': `XBL3.0 x=${credentials.userHash};${credentials.xstsToken}`,
            'x-xbl-contract-version': '2',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        };

        console.log('Fetching Xbox media:', { url, params, activityType });

        const response = await axios.get(url, {
            headers,
            params,
            timeout: 30000
        });

        const data: XboxApiResponse = response.data;

        // Transform Xbox API response to our format
        const items: XboxMediaItem[] = data.activityItems.map((item: any) => {
            const isScreenshot = activityType === 'Screenshot';
            
            return {
                id: isScreenshot ? item.screenshotId : item.clipId,
                type: isScreenshot ? 'screenshot' : 'gameclip',
                title: isScreenshot ? item.screenshotName : item.clipName,
                thumbnailUrl: isScreenshot ? item.screenshotThumbnail : item.clipThumbnail,
                downloadUrl: isScreenshot ? item.screenshotUri : item.downloadUri,
                gameTitle: item.contentTitle || 'Unknown Game',
                dateTaken: item.date,
                fileSize: item.gameMediaContentLocators?.[0]?.FileSize || 0,
                duration: isScreenshot ? undefined : item.durationInSeconds,
                resolutionWidth: item.resolutionWidth || 1920,
                resolutionHeight: item.resolutionHeight || 1080,
                platform: item.platform || 'Xbox',
                titleId: item.titleId || ''
            };
        });

        return {
            items,
            contToken: data.contToken
        };

    } catch (error) {
        console.error('Error fetching Xbox media:', error);
        if (axios.isAxiosError(error)) {
            console.error('Xbox API error response:', error.response?.data);
            console.error('Xbox API error status:', error.response?.status);
        }
        throw error;
    }
};

// Main handler for Xbox media endpoints
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Xbox media handler received event:', JSON.stringify(event, null, 2));

    const httpMethod = event.httpMethod;
    const resource = event.resource;

    try {
        if (httpMethod === 'GET' && resource === '/xbox/screenshots') {
            return await getXboxScreenshots(event);
        } else if (httpMethod === 'GET' && resource === '/xbox/gameclips') {
            return await getXboxGameClips(event);
        } else if (httpMethod === 'GET' && resource === '/xbox/media/{mediaId}/download') {
            return await downloadXboxMedia(event);
        } else {
            return createResponse(404, { error: 'Endpoint not found' });
        }
    } catch (error) {
        console.error('Xbox media handler error:', error);
        return createResponse(500, { 
            error: 'Internal server error', 
            details: (error as Error).message 
        });
    }
};
