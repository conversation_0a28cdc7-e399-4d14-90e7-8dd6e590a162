import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, UpdateCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { createResponse } from '../utils/response';
import { getUserIdFromContext } from '../utils/auth';
import { v4 as uuidv4 } from 'uuid';

// AWS Configuration
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

const USERS_TABLE = process.env.USERS_TABLE;
const XBOX_ACCOUNTS_TABLE = process.env.XBOX_ACCOUNTS_TABLE;

// TypeScript interfaces
interface XboxAuthRequest {
    accessToken: string;
    userToken: string;
    xstsToken: string;
    userHash: string;
    gamertag: string;
    xuid: string;
}

interface XboxAccount {
    id: string;
    userId: string;
    xboxUserId: string;
    gamertag: string;
    userHash: string;
    xstsToken: string;
    tokenExpiry: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

interface XboxMediaItem {
    id: string;
    type: 'screenshot' | 'gameclip';
    title: string;
    thumbnailUrl: string;
    downloadUrl: string;
    gameTitle: string;
    dateTaken: string;
    fileSize: number;
    duration?: number; // For game clips
    resolutionWidth: number;
    resolutionHeight: number;
}

// Link Xbox account to user
const linkXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('LinkXboxAccount: Received event body:', event.body);

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        let parsedBody: XboxAuthRequest;
        try {
            parsedBody = JSON.parse(event.body);
        } catch (parseError) {
            console.error('LinkXboxAccount: JSON parse error:', parseError);
            return createResponse(400, { error: 'Invalid JSON in request body' });
        }

        const { userToken, xstsToken, userHash, gamertag, xuid } = parsedBody;

        if (!userToken || !xstsToken || !userHash || !gamertag || !xuid) {
            return createResponse(400, { 
                error: 'Missing required fields: userToken, xstsToken, userHash, gamertag, xuid' 
            });
        }

        // Check if Xbox account is already linked to another user
        const existingXboxAccount = await checkExistingXboxAccount(xuid);
        if (existingXboxAccount && existingXboxAccount.userId !== userId) {
            return createResponse(409, { 
                error: 'This Xbox account is already linked to another user' 
            });
        }

        // Calculate token expiry (Xbox tokens typically last 24 hours)
        const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();

        const xboxAccountId = uuidv4();
        const now = new Date().toISOString();

        // Create or update Xbox account record
        const xboxAccount: XboxAccount = {
            id: xboxAccountId,
            userId,
            xboxUserId: xuid,
            gamertag,
            userHash,
            xstsToken,
            tokenExpiry,
            isActive: true,
            createdAt: now,
            updatedAt: now
        };

        if (existingXboxAccount) {
            // Update existing record
            const updateParams = {
                TableName: XBOX_ACCOUNTS_TABLE,
                Key: { id: existingXboxAccount.id },
                UpdateExpression: 'SET gamertag = :gamertag, userHash = :userHash, xstsToken = :xstsToken, tokenExpiry = :tokenExpiry, isActive = :isActive, updatedAt = :updatedAt',
                ExpressionAttributeValues: {
                    ':gamertag': gamertag,
                    ':userHash': userHash,
                    ':xstsToken': xstsToken,
                    ':tokenExpiry': tokenExpiry,
                    ':isActive': true,
                    ':updatedAt': now
                }
            };
            await dynamodb.send(new UpdateCommand(updateParams));
            xboxAccount.id = existingXboxAccount.id;
        } else {
            // Create new record
            const putCommand = new PutCommand({
                TableName: XBOX_ACCOUNTS_TABLE,
                Item: xboxAccount
            });
            await dynamodb.send(putCommand);
        }

        // Update user record with Xbox information
        const updateUserParams = {
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'SET xboxUserId = :xboxUserId, xboxGamertag = :xboxGamertag, xboxUserHash = :xboxUserHash, xboxXstsToken = :xboxXstsToken, xboxTokenExpiry = :xboxTokenExpiry, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':xboxUserId': xuid,
                ':xboxGamertag': gamertag,
                ':xboxUserHash': userHash,
                ':xboxXstsToken': xstsToken,
                ':xboxTokenExpiry': tokenExpiry,
                ':updatedAt': now
            }
        };
        await dynamodb.send(new UpdateCommand(updateUserParams));

        return createResponse(200, {
            message: 'Xbox account linked successfully',
            xboxAccount: {
                id: xboxAccount.id,
                gamertag,
                xboxUserId: xuid,
                isActive: true
            }
        });

    } catch (error) {
        console.error('LinkXboxAccount error:', error);
        return createResponse(500, { 
            error: 'Failed to link Xbox account', 
            details: (error as Error).message 
        });
    }
};

// Get user's linked Xbox account
const getLinkedXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get user record to check for Xbox linking
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Item;

        if (!user.xboxUserId) {
            return createResponse(404, { error: 'No Xbox account linked' });
        }

        // Check if token is still valid
        const tokenExpiry = new Date(user.xboxTokenExpiry);
        const now = new Date();
        const isTokenValid = tokenExpiry > now;

        return createResponse(200, {
            xboxAccount: {
                gamertag: user.xboxGamertag,
                xboxUserId: user.xboxUserId,
                isActive: true,
                tokenValid: isTokenValid,
                tokenExpiry: user.xboxTokenExpiry
            }
        });

    } catch (error) {
        console.error('GetLinkedXboxAccount error:', error);
        return createResponse(500, { 
            error: 'Failed to get Xbox account', 
            details: (error as Error).message 
        });
    }
};

// Helper function to check existing Xbox account
const checkExistingXboxAccount = async (xuid: string): Promise<XboxAccount | null> => {
    try {
        // This would require a GSI on xboxUserId in the XBOX_ACCOUNTS_TABLE
        // For now, we'll implement a simple check
        return null; // Placeholder - would need proper GSI implementation
    } catch (error) {
        console.error('Error checking existing Xbox account:', error);
        return null;
    }
};

// Unlink Xbox account
const unlinkXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Update user record to remove Xbox information
        const updateUserParams = {
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'REMOVE xboxUserId, xboxGamertag, xboxUserHash, xboxXstsToken, xboxTokenExpiry SET updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':updatedAt': new Date().toISOString()
            }
        };
        await dynamodb.send(new UpdateCommand(updateUserParams));

        return createResponse(200, {
            message: 'Xbox account unlinked successfully'
        });

    } catch (error) {
        console.error('UnlinkXboxAccount error:', error);
        return createResponse(500, { 
            error: 'Failed to unlink Xbox account', 
            details: (error as Error).message 
        });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Xbox handler received event:', JSON.stringify(event, null, 2));

    const httpMethod = event.httpMethod;
    const resource = event.resource;

    try {
        if (httpMethod === 'POST' && resource === '/xbox/link') {
            return await linkXboxAccount(event);
        } else if (httpMethod === 'GET' && resource === '/xbox/account') {
            return await getLinkedXboxAccount(event);
        } else if (httpMethod === 'DELETE' && resource === '/xbox/account') {
            return await unlinkXboxAccount(event);
        } else {
            return createResponse(404, { error: 'Endpoint not found' });
        }
    } catch (error) {
        console.error('Xbox handler error:', error);
        return createResponse(500, { 
            error: 'Internal server error', 
            details: (error as Error).message 
        });
    }
};
