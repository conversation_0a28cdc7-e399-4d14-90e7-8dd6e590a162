import { getHttpClient } from './utils/http-client';
import { TestContext } from './utils/test-context';

describe('Auth API Validation Tests', () => {
  let httpClient: ReturnType<typeof getHttpClient>;

  beforeAll(() => {
    // Initialize HTTP client if not already done
    try {
      httpClient = getHttpClient();
    } catch (error) {
      // Initialize if not already initialized
      const { initializeHttpClient } = require('./utils/http-client');
      const { testConfig } = require('./config/test.config');
      httpClient = initializeHttpClient({
        baseURL: testConfig.apiBaseUrl,
        timeout: testConfig.timeout,
      });
    }

    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('🔐 Starting Auth API validation tests...');
    }
  });

  describe('POST /auth/signup - Request Validation', () => {
    it('should reject request with missing required fields', async () => {
      const invalidData = {
        email: '<EMAIL>',
        // Missing password and username
      };

      try {
        await httpClient.post('/auth/signup', invalidData);
        fail('Expected request to fail due to missing required fields');
      } catch (error: any) {
        console.log('Missing fields error:', error.response.status, error.response.data);
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });

    it('should reject request with wrong data types', async () => {
      const invalidData = {
        email: 123, // Should be string
        password: 'ValidPass123!',
        username: 'testuser',
      };

      try {
        await httpClient.post('/auth/signup', invalidData);
        fail('Expected request to fail due to wrong data type');
      } catch (error: any) {
        console.log('Wrong type error:', error.response.status, error.response.data);
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });

    it('should accept request with valid structure (format validation happens in Lambda)', async () => {
      // Note: API Gateway only validates structure, not format constraints
      // Format validation (email, password strength, etc.) happens in the Lambda function
      const dataWithInvalidFormats = {
        email: 'invalid-email-format', // Invalid email format but valid string
        password: 'short', // Too short but valid string
        username: 'test user!', // Invalid characters but valid string
      };

      try {
        await httpClient.post('/auth/signup', dataWithInvalidFormats);
        fail('Expected Lambda to reject due to format validation');
      } catch (error: any) {
        console.log('Lambda validation error:', error.response.status, error.response.data);
        // Lambda should reject with business logic validation (400 or 500)
        expect([400, 500].includes(error.response.status)).toBe(true);
      }
    });

    it('should reject request with extra properties', async () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'ValidPass123!',
        username: 'testuser',
        extraField: 'not allowed', // Extra property
      };

      try {
        await httpClient.post('/auth/signup', invalidData);
        fail('Expected request to fail due to extra properties');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });

    it('should accept valid request with optional fields', async () => {
      const validData = {
        email: '<EMAIL>',
        password: 'ValidPass123!',
        username: 'validation_test_user',
        firstName: 'Test',
        lastName: 'User',
      };

      try {
        const response = await httpClient.post('/auth/signup', validData);
        expect(response.status).toBe(201);
        expect(response.data.message).toBe('User created successfully');
      } catch (error: any) {
        console.log('Signup validation error:', error.response?.status, error.response?.data);
        // User might already exist or other backend issues
        expect(error.response?.status).toBeDefined();
        expect([201, 400, 409, 500]).toContain(error.response?.status);
      }
    });
  });

  describe('POST /auth/signin - Request Validation', () => {
    it('should reject request with invalid email format', async () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'password',
      };

      try {
        await httpClient.post('/auth/signin', invalidData);
        fail('Expected request to fail due to invalid email');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });

    it('should reject request with empty password', async () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '', // Empty password
      };

      try {
        await httpClient.post('/auth/signin', invalidData);
        fail('Expected request to fail due to empty password');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });

    it('should reject request with extra properties', async () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'password',
        extraField: 'not allowed', // Extra property
      };

      try {
        await httpClient.post('/auth/signin', invalidData);
        fail('Expected request to fail due to extra properties');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });
  });

  describe('POST /auth/refresh - Request Validation', () => {
    it('should reject request without refresh token', async () => {
      const invalidData = {}; // Missing refreshToken

      try {
        await httpClient.post('/auth/refresh', invalidData);
        fail('Expected request to fail due to missing refresh token');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });

    it('should reject request with empty refresh token', async () => {
      const invalidData = {
        refreshToken: '', // Empty refresh token
      };

      try {
        await httpClient.post('/auth/refresh', invalidData);
        fail('Expected request to fail due to empty refresh token');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        // Error message format may vary between API Gateway and Lambda
        if (error.response.data.message) {
          expect(error.response.data.message).toBeDefined();
        } else if (error.response.data.error) {
          expect(error.response.data.error).toBeDefined();
        }
      }
    });

    it('should reject request with extra properties', async () => {
      const invalidData = {
        refreshToken: 'some-token',
        extraField: 'not allowed', // Extra property
      };

      try {
        await httpClient.post('/auth/refresh', invalidData);
        fail('Expected request to fail due to extra properties');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });
  });

  describe('GET /auth/validate - Request Validation', () => {
    it('should reject request without Authorization header', async () => {
      // Temporarily remove auth token
      httpClient.removeAuthToken();

      try {
        await httpClient.get('/auth/validate');
        fail('Expected request to fail due to missing Authorization header');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        // Error message may vary - could be "Invalid request" or "Missing required request parameters"
        expect(error.response.data.message).toBeDefined();
        expect(error.response.data.message.length).toBeGreaterThan(0);
      }

      // Restore auth token if available
      try {
        const accessToken = TestContext.getAccessToken();
        if (accessToken) {
          httpClient.setAuthToken(accessToken);
        }
      } catch (error) {
        // Test user not initialized yet, that's okay
      }
    });

    it('should reject request with malformed Authorization header', async () => {
      // Set malformed auth header
      let originalToken;
      try {
        originalToken = TestContext.getAccessToken();
      } catch (error) {
        // Test user not initialized yet, that's okay
      }
      httpClient.setAuthToken('invalid-token-format');

      try {
        await httpClient.get('/auth/validate');
        fail('Expected request to fail due to malformed Authorization header');
      } catch (error: any) {
        // API Gateway may return 400 or 401 for malformed authorization
        expect([400, 401]).toContain(error.response.status);
        // Error message may vary - could be "Invalid request" or other format
        if (error.response.data.message) {
          expect(error.response.data.message).toBeDefined();
        } else if (error.response.data.error) {
          expect(error.response.data.error).toBeDefined();
        }
      }

      // Restore original token
      if (originalToken) {
        httpClient.setAuthToken(originalToken);
      }
    });
  });

  afterAll(() => {
    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('✅ Auth API validation tests completed');
    }
  });
});
