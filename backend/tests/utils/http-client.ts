import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { HttpClientConfig, HttpResponse } from '../types/api.types';

export class HttpClient {
  private client: AxiosInstance;

  constructor(config: HttpClientConfig) {
    this.client = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    });

    // Request interceptor for minimal logging
    this.client.interceptors.request.use(
      (config) => {
        // Only log on errors or when VERBOSE_TESTS env var is set
        if (process.env.VERBOSE_TESTS === 'true') {
          console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`);
          if (config.data) {
            console.log('📤 Request body:', JSON.stringify(config.data, null, 2));
          }
        }
        return config;
      },
      (error) => {
        console.error('❌ Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for minimal logging
    this.client.interceptors.response.use(
      (response) => {
        // Only log on errors or when VERBOSE_TESTS env var is set
        if (process.env.VERBOSE_TESTS === 'true') {
          console.log(`✅ ${response.status} ${response.statusText}`);
          console.log('📥 Response body:', JSON.stringify(response.data, null, 2));
        }
        return response;
      },
      (error) => {
        if (error.response) {
          console.error(`❌ ${error.response.status} ${error.response.statusText}`);
          console.error('📥 Error response:', JSON.stringify(error.response.data, null, 2));
        } else {
          console.error('❌ Network error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<HttpResponse<T>> {
    const response: AxiosResponse<T> = await this.client.get(url, config);
    return this.transformResponse(response);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<HttpResponse<T>> {
    const response: AxiosResponse<T> = await this.client.post(url, data, config);
    return this.transformResponse(response);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<HttpResponse<T>> {
    const response: AxiosResponse<T> = await this.client.put(url, data, config);
    return this.transformResponse(response);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<HttpResponse<T>> {
    const response: AxiosResponse<T> = await this.client.delete(url, config);
    return this.transformResponse(response);
  }

  setAuthToken(token: string): void {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  removeAuthToken(): void {
    delete this.client.defaults.headers.common['Authorization'];
  }

  private transformResponse<T>(response: AxiosResponse<T>): HttpResponse<T> {
    return {
      status: response.status,
      statusText: response.statusText,
      data: response.data,
      headers: response.headers as Record<string, string>,
    };
  }
}

// Singleton instance for tests
let httpClientInstance: HttpClient | null = null;

export function getHttpClient(): HttpClient {
  if (!httpClientInstance) {
    throw new Error('HTTP client not initialized. Call initializeHttpClient first.');
  }
  return httpClientInstance;
}

export function initializeHttpClient(config: HttpClientConfig): HttpClient {
  httpClientInstance = new HttpClient(config);
  return httpClientInstance;
}
