# GameFlex Backend - AWS CDK

A serverless backend for the GameFlex mobile application built with AWS CDK (Cloud Development Kit) using TypeScript.

## 🏗️ Architecture

This backend uses AWS CDK to deploy and manage:

- **AWS Lambda Functions** - Serverless compute for API endpoints
- **Amazon API Gateway** - REST API with custom authorizers
- **Amazon DynamoDB** - NoSQL database for application data
- **Amazon Cognito** - User authentication and management
- **AWS Secrets Manager** - Secure configuration storage
- **CloudFlare R2** - Object storage for media files

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18 or later)
- **npm** (v8 or later)
- **AWS CLI** (v2 or later)
- **AWS CDK CLI** (v2 or later)

### Install AWS CDK CLI

```bash
npm install -g aws-cdk
```

### Configure AWS Credentials

```bash
aws configure
```

Or set environment variables:
```bash
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-west-2
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Bootstrap CDK (First time only)

```bash
cdk bootstrap
```

### 3. Build the Project

```bash
npm run build
```

### 4. Deploy to Development

```bash
npm run deploy:dev
```

## 📁 Project Structure

```
backend/
├── bin/                    # CDK app entry point
│   └── cdk.ts             # Main CDK application
├── lib/                    # CDK stack definitions
│   └── gameflex-backend-stack.ts
├── src/                    # Lambda function source code
│   ├── auth/              # Authentication functions
│   ├── authorizer/        # API Gateway authorizer
│   ├── channels/          # Channel management
│   ├── health/            # Health check endpoint
│   ├── media/             # Media upload/management
│   ├── posts/             # Post management
│   ├── reflexes/          # Reflex (reaction) system
│   ├── users/             # User management
│   └── utils/             # Shared utilities
├── test/                   # CDK unit tests
├── deploy.sh              # Unified deployment script (supports all environments)
└── cdk.json              # CDK configuration
```

## 🛠️ Development Commands

### Build and Compilation

```bash
npm run build          # Compile TypeScript
npm run watch          # Watch for changes and compile
```

### Testing

```bash
npm test               # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Run tests with coverage
npm run test:unit      # Run unit tests only
```

### CDK Commands

```bash
npm run synth          # Synthesize CloudFormation template
npm run diff           # Show differences with deployed stack
npm run destroy        # Destroy the stack
```

## 🚀 Deployment

The project uses a unified deployment script that supports all environments. Development is the default environment.

### Development Environment (Default)

```bash
./deploy.sh
# or
./deploy.sh development
# or
npm run deploy:dev
```

### Staging Environment

```bash
./deploy.sh staging
# or
npm run deploy:staging
```

### Production Environment

```bash
./deploy.sh production
# or
npm run deploy:production
```

### Deployment Options

All deployment scripts support the following options:

- `-h, --help` - Show help message
- `-y, --yes` - Skip confirmation prompts
- `--diff` - Show diff before deployment
- `--import` - Import existing resources

Examples:
```bash
./deploy.sh --diff              # Show changes before deploying to development
./deploy.sh staging -y          # Deploy to staging without confirmation
./deploy.sh production --diff   # Show changes before deploying to production
./deploy.sh --synth-only        # Only synthesize, don't deploy
```

## 🔧 Configuration

### Environment Variables

The CDK stack uses context variables for configuration:

- `environment` - Target environment (development, staging, production)
- `projectName` - Project name prefix for resources

### Lambda Function Environment Variables

Each Lambda function receives these environment variables:

- `ENVIRONMENT` - Current environment
- `PROJECT_NAME` - Project name
- `USER_POOL_ID` - Cognito User Pool ID
- `USER_POOL_CLIENT_ID` - Cognito User Pool Client ID
- `USERS_TABLE` - Users DynamoDB table name
- `POSTS_TABLE` - Posts DynamoDB table name
- `MEDIA_TABLE` - Media DynamoDB table name
- `R2_SECRET_NAME` - CloudFlare R2 configuration secret name
- `APP_CONFIG_SECRET_NAME` - Application configuration secret name

### Secrets Manager Configuration

The stack creates two secrets in AWS Secrets Manager:

1. **R2 Configuration** (`{projectName}-r2-config-{environment}`)
   ```json
   {
     "accountId": "your_cloudflare_account_id",
     "accessKeyId": "your_r2_access_key",
     "secretAccessKey": "your_r2_secret_key",
     "endpoint": "https://your_account_id.r2.cloudflarestorage.com",
     "bucketName": "your-bucket-name",
     "publicUrl": "https://your-public-domain.com"
   }
   ```

2. **App Configuration** (`{projectName}-app-config-{environment}`)
   ```json
   {
     "jwtSecret": "your_jwt_secret_key",
     "apiVersion": "v1",
     "corsOrigins": ["https://your-frontend-domain.com"]
   }
   ```

## 🗄️ Database Schema

### DynamoDB Tables

The stack creates the following DynamoDB tables:

1. **Users** - User account information
2. **UserProfiles** - Extended user profile data
3. **Posts** - User posts and content
4. **Media** - Media file metadata
5. **Comments** - Post comments
6. **Likes** - Post and comment likes
7. **Follows** - User follow relationships
8. **Channels** - Community channels
9. **ChannelMembers** - Channel membership
10. **Reflexes** - User reactions/reflexes

### Global Secondary Indexes (GSI)

- **Posts**: `user-id-index`, `channel-id-index`, `created-at-index`
- **Media**: `user-id-index`, `post-id-index`
- **Comments**: `post-id-index`, `user-id-index`
- **Likes**: `user-id-index`, `post-id-index`
- **Follows**: `follower-id-index`, `following-id-index`
- **ChannelMembers**: `user-id-index`, `channel-id-index`

## 🔐 Authentication

The backend uses Amazon Cognito for user authentication with:

- **User Pool** - Manages user accounts and authentication
- **User Pool Client** - Configured for mobile app integration
- **API Gateway Authorizer** - Custom Lambda authorizer for JWT validation

### Authentication Flow

1. Users sign up/sign in through Cognito
2. Cognito returns JWT tokens
3. Mobile app includes JWT in API requests
4. API Gateway authorizer validates JWT
5. Lambda functions receive user context

## 🌐 API Endpoints

### Base URL Structure

- **Development**: `https://dev.api.gameflex.io`
- **Staging**: `https://staging.api.gameflex.io`
- **Production**: `https://api.gameflex.io`

### Available Endpoints

- `GET /health` - Health check (no auth required)
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/signout` - User logout
- `GET /auth/me` - Get current user info
- `GET /users/{id}` - Get user profile
- `PUT /users/{id}` - Update user profile
- `GET /posts` - List posts
- `POST /posts` - Create post
- `GET /posts/{id}` - Get specific post
- `PUT /posts/{id}` - Update post
- `DELETE /posts/{id}` - Delete post
- `GET /channels` - List channels
- `POST /channels` - Create channel
- `GET /media/upload-url` - Get presigned upload URL
- `POST /media` - Create media record

## 🧪 Testing

### Unit Tests

Run CDK unit tests to validate infrastructure:

```bash
npm test
```

### Integration Tests

Test against deployed infrastructure:

```bash
npm run test:integration
```

### API Testing

Test API endpoints manually:

```bash
# Health check
curl https://your-api-url/health

# With authentication
curl -H "Authorization: Bearer your-jwt-token" \
     https://your-api-url/auth/me
```

## 🔍 Monitoring and Logging

### CloudWatch Logs

Lambda function logs are automatically sent to CloudWatch Logs:

- Log Group: `/aws/lambda/{function-name}`
- Retention: 14 days (configurable per environment)

### Monitoring Stack Outputs

After deployment, get important information:

```bash
aws cloudformation describe-stacks \
  --stack-name gameflex-development \
  --query 'Stacks[0].Outputs' \
  --output table
```

## 🚨 Troubleshooting

### Common Issues

#### 1. CDK Bootstrap Required

**Error**: `Need to perform AWS CDK bootstrap`

**Solution**:
```bash
cdk bootstrap aws://ACCOUNT-NUMBER/REGION
```

#### 2. Insufficient Permissions

**Error**: `User is not authorized to perform: cloudformation:CreateStack`

**Solution**: Ensure your AWS user/role has the necessary permissions:
- CloudFormation full access
- IAM permissions for resource creation
- Service-specific permissions (Lambda, API Gateway, DynamoDB, etc.)

#### 3. Lambda Function Dependencies

**Error**: Lambda function fails to import modules

**Solution**: Ensure dependencies are installed in each Lambda directory:
```bash
cd src/auth && npm install
cd src/posts && npm install
# etc.
```

#### 4. Environment Variable Issues

**Error**: Lambda functions can't access environment variables

**Solution**: Check that environment variables are properly set in the CDK stack and Lambda functions are using the correct variable names.

### Debugging

#### Enable Verbose Logging

```bash
export CDK_DEBUG=true
./deploy.sh --verbose
# or for specific environment
./deploy.sh staging --verbose
```

#### Check Lambda Logs

```bash
aws logs tail /aws/lambda/gameflex-development-auth --follow
```

#### Validate CloudFormation Template

```bash
npm run synth
```

## 🔄 Migration from SAM

This project has been migrated from AWS SAM to AWS CDK. Key changes:

### What Changed

1. **Infrastructure as Code**: `template.yaml` → TypeScript CDK stacks
2. **Deployment**: `sam deploy` → `cdk deploy`
3. **Build Process**: `sam build` → `npm run build`
4. **Configuration**: `samconfig.toml` → CDK context and environment variables
5. **Testing**: SAM local → CDK unit tests + integration tests

### What Stayed the Same

1. **Lambda Functions**: Source code remains unchanged
2. **API Structure**: Same endpoints and request/response formats
3. **Database Schema**: DynamoDB table structures unchanged
4. **Authentication**: Cognito configuration preserved

### Migration Benefits

- **Type Safety**: TypeScript provides compile-time error checking
- **Better IDE Support**: IntelliSense and auto-completion
- **Programmatic Infrastructure**: More flexible than YAML templates
- **Easier Testing**: Built-in unit testing for infrastructure
- **Better Dependency Management**: npm ecosystem integration

## 📚 Additional Resources

- [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/)
- [AWS CDK TypeScript Reference](https://docs.aws.amazon.com/cdk/api/v2/typescript/)
- [AWS Lambda Documentation](https://docs.aws.amazon.com/lambda/)
- [Amazon API Gateway Documentation](https://docs.aws.amazon.com/apigateway/)
- [Amazon DynamoDB Documentation](https://docs.aws.amazon.com/dynamodb/)
- [Amazon Cognito Documentation](https://docs.aws.amazon.com/cognito/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Run tests: `npm test`
5. Commit your changes: `git commit -am 'Add new feature'`
6. Push to the branch: `git push origin feature/new-feature`
7. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

1. Check the troubleshooting section above
2. Review AWS CDK documentation
3. Check existing GitHub issues
4. Create a new issue with detailed information

---

**GameFlex Backend** - Built with ❤️ using AWS CDK
