{"Version": "2012-10-17", "Statement": [{"Sid": "CloudFormationAccess", "Effect": "Allow", "Action": "cloudformation:*", "Resource": "*"}, {"Sid": "S3Access", "Effect": "Allow", "Action": "s3:*", "Resource": "*"}, {"Sid": "IAMAccess", "Effect": "Allow", "Action": "iam:*", "Resource": "*"}, {"Sid": "LambdaAccess", "Effect": "Allow", "Action": "lambda:*", "Resource": "*"}, {"Sid": "APIGatewayAccess", "Effect": "Allow", "Action": "apigateway:*", "Resource": "*"}, {"Sid": "DynamoDBAccess", "Effect": "Allow", "Action": "dynamodb:*", "Resource": "*"}, {"Sid": "CognitoAccess", "Effect": "Allow", "Action": "cognito-idp:*", "Resource": "*"}, {"Sid": "SecretsManagerAccess", "Effect": "Allow", "Action": "secretsmanager:*", "Resource": "*"}, {"Sid": "LogsAccess", "Effect": "Allow", "Action": "logs:*", "Resource": "*"}, {"Sid": "ECRAccess", "Effect": "Allow", "Action": "ecr:*", "Resource": "*"}, {"Sid": "SSMAccess", "Effect": "Allow", "Action": "ssm:*", "Resource": "*"}, {"Sid": "STSAccess", "Effect": "Allow", "Action": ["sts:<PERSON><PERSON>Role", "sts:GetCallerIdentity"], "Resource": "*"}, {"Sid": "KMSAccess", "Effect": "Allow", "Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey", "kms:ReEncrypt*"], "Resource": "*"}, {"Sid": "EventsAccess", "Effect": "Allow", "Action": "events:*", "Resource": "*"}, {"Sid": "ApplicationAutoScalingAccess", "Effect": "Allow", "Action": "application-autoscaling:*", "Resource": "*"}, {"Sid": "DenyOtherEnvironments", "Effect": "<PERSON><PERSON>", "Action": "*", "Resource": ["arn:aws:cloudformation:*:*:stack/gameflex-dev*/*", "arn:aws:cloudformation:*:*:stack/gameflex-prod*/*", "arn:aws:s3:::gameflex-dev*", "arn:aws:s3:::gameflex-prod*", "arn:aws:lambda:*:*:function:gameflex-dev*", "arn:aws:lambda:*:*:function:gameflex-prod*", "arn:aws:dynamodb:*:*:table/gameflex-dev*", "arn:aws:dynamodb:*:*:table/gameflex-prod*", "arn:aws:iam::*:role/gameflex-dev*", "arn:aws:iam::*:role/gameflex-prod*"]}]}