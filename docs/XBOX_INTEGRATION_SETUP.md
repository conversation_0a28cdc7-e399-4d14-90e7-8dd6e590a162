# Xbox Integration Setup Guide

This guide will walk you through setting up Xbox Live API integration for GameFlex, allowing users to link their Xbox accounts and browse/download their screenshots and game clips.

## Prerequisites

- Microsoft Azure account
- Xbox Live developer access
- GameFlex backend deployed
- Flutter development environment

## Step 1: Xbox Live Developer Setup (Do This First!)

### 1.1 Xbox Live Developer Account

**Important: You must complete this step before setting up Azure AD permissions!**

1. Visit [Xbox Live Developer Portal](https://developer.microsoft.com/en-us/games/)
2. Sign in with your Microsoft account
3. Complete the developer registration process:
   - Accept the Xbox Live developer agreement
   - Provide business/developer information
   - Wait for approval (this can take 1-3 business days)

### 1.2 Create Xbox Live Application

1. Once approved, in the Xbox Live portal:
   - Click **Create a new title**
   - Fill in your application details:
     - **Product name**: `GameFlex`
     - **Product type**: `Game` or `Application`
     - **Platforms**: Select appropriate platforms
2. Note down these important values:
   - **Title ID**: You'll need this for API calls
   - **SCID** (Service Configuration ID): Also needed for API calls
   - **Sandbox**: Set to `RETAIL` for production

### 1.3 Configure Xbox Live Services

1. In your Xbox Live application settings:
   - Enable **Xbox Live Services**
   - Configure **Privacy and Online Safety** settings
   - Set up **Achievements** (if needed)
   - Configure **Leaderboards** (if needed)

## Step 2: Azure Active Directory Application Setup

### 2.1 Create Azure AD Application

1. Go to the [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Fill in the application details:
   - **Name**: `GameFlex Xbox Integration`
   - **Supported account types**: `Accounts in any organizational directory and personal Microsoft accounts`
   - **Redirect URI**:
     - Type: `Web`
     - URI: `https://your-app-domain.com/xbox/callback` (replace with your actual domain)

### 2.2 Configure Application

1. After creation, note down the **Application (client) ID**
2. Go to **Certificates & secrets**
3. Click **New client secret**
4. Add a description and set expiration
5. **Copy the secret value immediately** (you won't be able to see it again)

### 2.3 Configure API Permissions

**Note: These permissions will only be available after you have Xbox Live developer access.**

1. Go to **API permissions**
2. Click **Add a permission**
3. Select **Microsoft APIs**
4. Look for **Xbox Live API** (it may also appear under **APIs my organization uses** after Xbox Live developer approval)
5. If you don't see Xbox Live API, you can add these Microsoft Graph permissions instead:
   - `User.Read`
   - `offline_access`
6. For Xbox Live specific permissions (when available):
   - `Xboxlive.signin`
   - `Xboxlive.offline_access`

**Alternative Approach if Xbox Live API is not visible:**
- You can proceed with basic Microsoft account authentication
- The Xbox Live tokens will be obtained through the standard OAuth flow
- Xbox Live API access is granted through your developer account, not Azure AD permissions

## Step 3: Backend Configuration

### 3.1 Environment Variables

Add the following environment variables to your backend:

```bash
# Xbox Live Configuration
XBOX_CLIENT_ID=your_azure_ad_client_id
XBOX_CLIENT_SECRET=your_azure_ad_client_secret
XBOX_REDIRECT_URI=https://your-app-domain.com/xbox/callback
XBOX_TITLE_ID=your_xbox_title_id
XBOX_SCID=your_xbox_scid
```

### 3.2 Deploy Backend Changes

1. Deploy the updated backend with Xbox functionality:
   ```bash
   cd backend
   npm run deploy:staging  # or deploy:production
   ```

2. Verify the new Xbox endpoints are available:
   - `POST /xbox/link` - Link Xbox account
   - `GET /xbox/account` - Get linked account info
   - `DELETE /xbox/account` - Unlink Xbox account
   - `GET /xbox/screenshots` - Get user screenshots
   - `GET /xbox/gameclips` - Get user game clips

## Step 4: Frontend Configuration

### 4.1 Environment Configuration

Add Xbox configuration to your Flutter app's environment files:

```dart
// lib/config/environment_config.dart
class EnvironmentConfig {
  // ... existing config
  
  static const String xboxClientId = String.fromEnvironment(
    'XBOX_CLIENT_ID',
    defaultValue: 'your_xbox_client_id_here',
  );
  
  static const String xboxRedirectUri = String.fromEnvironment(
    'XBOX_REDIRECT_URI', 
    defaultValue: 'https://your-app-domain.com/xbox/callback',
  );
}
```

### 4.2 Add Dependencies

Ensure your `pubspec.yaml` includes necessary dependencies:

```yaml
dependencies:
  # ... existing dependencies
  http: ^1.1.0
  cached_network_image: ^3.3.0
  url_launcher: ^6.2.1  # For OAuth flow
```

## Step 5: Testing Xbox Integration

### 5.1 Test OAuth Flow

1. Create a test Xbox account or use your existing one
2. Test the OAuth flow:
   ```dart
   final authUrl = XboxAuthService.instance.generateAuthUrl(
     clientId: 'your_client_id',
     redirectUri: 'your_redirect_uri',
   );
   // Launch this URL in a web browser
   ```

### 5.2 Test API Endpoints

Use tools like Postman or curl to test the backend endpoints:

```bash
# Test linking Xbox account (requires valid tokens)
curl -X POST https://your-api-domain.com/v1/xbox/link \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "accessToken": "xbox_access_token",
    "userToken": "xbox_user_token", 
    "xstsToken": "xbox_xsts_token",
    "userHash": "xbox_user_hash",
    "gamertag": "YourGamertag",
    "xuid": "xbox_user_id"
  }'

# Test getting screenshots
curl -X GET https://your-api-domain.com/v1/xbox/screenshots \
  -H "Authorization: Bearer your_jwt_token"
```

## Step 6: Production Deployment

### 6.1 Security Considerations

1. **Never expose client secrets** in frontend code
2. **Use HTTPS** for all OAuth redirects
3. **Validate tokens** on the backend
4. **Implement rate limiting** for Xbox API calls
5. **Store tokens securely** with proper encryption

### 6.2 Monitoring and Logging

1. Set up monitoring for Xbox API calls
2. Log authentication failures and token refresh events
3. Monitor API rate limits and usage

## Troubleshooting

### Common Issues

1. **"Xbox Live API not found in Azure AD permissions"**
   - **Cause**: You haven't been approved for Xbox Live developer access yet
   - **Solution**: Complete Xbox Live developer registration first
   - **Alternative**: Use basic Microsoft account OAuth with `User.Read` and `offline_access` permissions
   - **Note**: Xbox Live API access is granted through your developer account, not Azure AD

2. **"Invalid client" error**
   - Verify your Azure AD client ID and secret
   - Check that the redirect URI matches exactly

3. **"Unauthorized" when calling Xbox APIs**
   - Ensure tokens are valid and not expired
   - Check that the user has granted necessary permissions
   - Verify your Xbox Live developer account is active

4. **"No media found"**
   - Verify the user has screenshots/clips on their Xbox account
   - Check that the Xbox Live API is returning data
   - Ensure the user's Xbox privacy settings allow API access

5. **Token expiration issues**
   - Implement proper token refresh logic
   - Xbox tokens typically expire after 24 hours

6. **Xbox Live developer registration pending**
   - Registration can take 1-3 business days
   - You can start with basic Microsoft account authentication
   - Xbox Live specific features will be available after approval

### Debug Steps

1. **Check backend logs** for API call errors
2. **Verify token validity** using Xbox Live token validation endpoints
3. **Test with Xbox Live API directly** using tools like Postman
4. **Check network connectivity** and firewall settings

## API Rate Limits

Xbox Live API has rate limits:
- **User-based calls**: 300 requests per 300 seconds per user
- **Title-based calls**: 30,000 requests per 300 seconds per title

Implement proper caching and rate limiting in your application.

## Support and Resources

- [Xbox Live API Documentation](https://docs.microsoft.com/en-us/gaming/xbox-live/)
- [Azure AD OAuth Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/)
- [Xbox Live Developer Forums](https://forums.xboxlive.com/)

## Security Best Practices

1. **Token Storage**: Store Xbox tokens securely with encryption
2. **API Keys**: Never commit API keys to version control
3. **HTTPS Only**: Always use HTTPS for OAuth flows
4. **Token Validation**: Validate all tokens on the backend
5. **Rate Limiting**: Implement proper rate limiting to avoid API abuse

## Next Steps

After completing this setup:

1. Test the complete flow from Xbox authentication to media browsing
2. Implement error handling for various failure scenarios
3. Add user feedback for authentication status
4. Consider implementing background token refresh
5. Add analytics to track Xbox integration usage
