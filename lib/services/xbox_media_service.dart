import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../models/xbox_media_model.dart';
import '../utils/app_logger.dart';
import 'api_service.dart';

/// Service for fetching Xbox screenshots and game clips
class XboxMediaService {
  static final XboxMediaService _instance = XboxMediaService._internal();
  static XboxMediaService get instance => _instance;
  XboxMediaService._internal();

  /// Get Xbox screenshots for the authenticated user
  Future<XboxMediaResponse?> getScreenshots({
    int numItems = 25,
    String? contToken,
  }) async {
    try {
      developer.log('XboxMediaService: Fetching Xbox screenshots');
      AppLogger.info('Fetching Xbox screenshots');

      final queryParams = <String, String>{
        'numItems': numItems.toString(),
      };

      if (contToken != null && contToken.isNotEmpty) {
        queryParams['contToken'] = contToken;
      }

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/screenshots',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        developer.log('XboxMediaService: Successfully fetched ${data['screenshots']?.length ?? 0} screenshots');
        AppLogger.info('Successfully fetched Xbox screenshots');

        final screenshots = (data['screenshots'] as List<dynamic>?)
            ?.map((item) => XboxMediaItem.fromJson(item as Map<String, dynamic>))
            .toList() ?? [];

        final pagination = data['pagination'] as Map<String, dynamic>?;

        return XboxMediaResponse(
          items: screenshots,
          hasMore: pagination?['hasMore'] as bool? ?? false,
          contToken: pagination?['contToken'] as String?,
        );
      } else if (response.statusCode == 404) {
        developer.log('XboxMediaService: No Xbox account linked');
        AppLogger.info('No Xbox account linked for screenshots');
        return null;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log('XboxMediaService: Failed to fetch screenshots: ${data['error']}');
        AppLogger.error('Failed to fetch Xbox screenshots', error: data['error']);
        return null;
      }
    } catch (e) {
      developer.log('XboxMediaService: Error fetching screenshots: $e');
      AppLogger.error('Error fetching Xbox screenshots', error: e);
      return null;
    }
  }

  /// Get Xbox game clips for the authenticated user
  Future<XboxMediaResponse?> getGameClips({
    int numItems = 25,
    String? contToken,
  }) async {
    try {
      developer.log('XboxMediaService: Fetching Xbox game clips');
      AppLogger.info('Fetching Xbox game clips');

      final queryParams = <String, String>{
        'numItems': numItems.toString(),
      };

      if (contToken != null && contToken.isNotEmpty) {
        queryParams['contToken'] = contToken;
      }

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/gameclips',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        developer.log('XboxMediaService: Successfully fetched ${data['gameClips']?.length ?? 0} game clips');
        AppLogger.info('Successfully fetched Xbox game clips');

        final gameClips = (data['gameClips'] as List<dynamic>?)
            ?.map((item) => XboxMediaItem.fromJson(item as Map<String, dynamic>))
            .toList() ?? [];

        final pagination = data['pagination'] as Map<String, dynamic>?;

        return XboxMediaResponse(
          items: gameClips,
          hasMore: pagination?['hasMore'] as bool? ?? false,
          contToken: pagination?['contToken'] as String?,
        );
      } else if (response.statusCode == 404) {
        developer.log('XboxMediaService: No Xbox account linked');
        AppLogger.info('No Xbox account linked for game clips');
        return null;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log('XboxMediaService: Failed to fetch game clips: ${data['error']}');
        AppLogger.error('Failed to fetch Xbox game clips', error: data['error']);
        return null;
      }
    } catch (e) {
      developer.log('XboxMediaService: Error fetching game clips: $e');
      AppLogger.error('Error fetching Xbox game clips', error: e);
      return null;
    }
  }

  /// Get download URL for Xbox media item
  Future<String?> getMediaDownloadUrl(String mediaId) async {
    try {
      developer.log('XboxMediaService: Getting download URL for media: $mediaId');
      AppLogger.info('Getting Xbox media download URL');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/media/$mediaId/download',
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        final downloadUrl = data['downloadUrl'] as String?;
        
        developer.log('XboxMediaService: Successfully got download URL');
        AppLogger.info('Successfully got Xbox media download URL');
        
        return downloadUrl;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log('XboxMediaService: Failed to get download URL: ${data['error']}');
        AppLogger.error('Failed to get Xbox media download URL', error: data['error']);
        return null;
      }
    } catch (e) {
      developer.log('XboxMediaService: Error getting download URL: $e');
      AppLogger.error('Error getting Xbox media download URL', error: e);
      return null;
    }
  }

  /// Download Xbox media item to local storage
  Future<String?> downloadMedia(XboxMediaItem mediaItem) async {
    try {
      developer.log('XboxMediaService: Downloading Xbox media: ${mediaItem.id}');
      AppLogger.info('Downloading Xbox media item');

      // Get the download URL first
      final downloadUrl = await getMediaDownloadUrl(mediaItem.id);
      if (downloadUrl == null) {
        developer.log('XboxMediaService: Failed to get download URL');
        AppLogger.error('Failed to get download URL for Xbox media');
        return null;
      }

      // For now, return the download URL
      // In a full implementation, you would download the file and save it locally
      developer.log('XboxMediaService: Media download URL obtained: $downloadUrl');
      AppLogger.info('Xbox media download URL obtained');
      
      return downloadUrl;
    } catch (e) {
      developer.log('XboxMediaService: Error downloading media: $e');
      AppLogger.error('Error downloading Xbox media', error: e);
      return null;
    }
  }

  /// Get combined media (screenshots and game clips) for browsing
  Future<List<XboxMediaItem>> getAllMedia({
    int screenshotLimit = 15,
    int gameClipLimit = 10,
  }) async {
    try {
      developer.log('XboxMediaService: Fetching all Xbox media');
      AppLogger.info('Fetching all Xbox media');

      final List<XboxMediaItem> allMedia = [];

      // Fetch screenshots
      final screenshotsResponse = await getScreenshots(numItems: screenshotLimit);
      if (screenshotsResponse != null) {
        allMedia.addAll(screenshotsResponse.items);
      }

      // Fetch game clips
      final gameClipsResponse = await getGameClips(numItems: gameClipLimit);
      if (gameClipsResponse != null) {
        allMedia.addAll(gameClipsResponse.items);
      }

      // Sort by date taken (most recent first)
      allMedia.sort((a, b) => b.dateTaken.compareTo(a.dateTaken));

      developer.log('XboxMediaService: Successfully fetched ${allMedia.length} total media items');
      AppLogger.info('Successfully fetched all Xbox media');

      return allMedia;
    } catch (e) {
      developer.log('XboxMediaService: Error fetching all media: $e');
      AppLogger.error('Error fetching all Xbox media', error: e);
      return [];
    }
  }

  /// Filter media by game title
  List<XboxMediaItem> filterByGame(List<XboxMediaItem> media, String gameTitle) {
    return media.where((item) => 
      item.gameTitle.toLowerCase().contains(gameTitle.toLowerCase())
    ).toList();
  }

  /// Filter media by type
  List<XboxMediaItem> filterByType(List<XboxMediaItem> media, XboxMediaType type) {
    return media.where((item) => item.type == type).toList();
  }

  /// Get unique game titles from media list
  List<String> getUniqueGameTitles(List<XboxMediaItem> media) {
    final Set<String> gameTitles = {};
    for (final item in media) {
      if (item.gameTitle.isNotEmpty && item.gameTitle != 'Unknown Game') {
        gameTitles.add(item.gameTitle);
      }
    }
    return gameTitles.toList()..sort();
  }
}
