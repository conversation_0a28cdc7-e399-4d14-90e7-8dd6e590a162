import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../utils/app_logger.dart';
import 'api_service.dart';

/// Xbox authentication service for linking Xbox accounts
class XboxAuthService {
  static final XboxAuthService _instance = XboxAuthService._internal();
  static XboxAuthService get instance => _instance;
  XboxAuthService._internal();

  // Xbox Live OAuth endpoints
  static const String _xboxLiveAuthorizeUrl = 'https://login.live.com/oauth20_authorize.srf';
  static const String _xboxLiveTokenUrl = 'https://login.live.com/oauth20_token.srf';
  static const String _xboxUserAuthUrl = 'https://user.auth.xboxlive.com/user/authenticate';
  static const String _xboxXstsAuthUrl = 'https://xsts.auth.xboxlive.com/xsts/authorize';

  // Xbox Live scopes
  static const List<String> _defaultScopes = [
    'Xboxlive.signin',
    'Xboxlive.offline_access'
  ];

  /// Generate Xbox Live authorization URL
  String generateAuthUrl({
    required String clientId,
    required String redirectUri,
    String? state,
    List<String>? scopes,
  }) {
    final queryParams = <String, String>{
      'client_id': clientId,
      'response_type': 'code',
      'approval_prompt': 'auto',
      'scope': (scopes ?? _defaultScopes).join(' '),
      'redirect_uri': redirectUri,
    };

    if (state != null && state.isNotEmpty) {
      queryParams['state'] = state;
    }

    final uri = Uri.parse(_xboxLiveAuthorizeUrl).replace(
      queryParameters: queryParams,
    );

    developer.log('XboxAuthService: Generated auth URL: $uri');
    AppLogger.info('Generated Xbox Live auth URL');

    return uri.toString();
  }

  /// Exchange authorization code for OAuth token
  Future<Map<String, dynamic>?> exchangeCodeForToken({
    required String clientId,
    required String clientSecret,
    required String authorizationCode,
    required String redirectUri,
    List<String>? scopes,
  }) async {
    try {
      developer.log('XboxAuthService: Exchanging authorization code for token');
      AppLogger.info('Exchanging Xbox authorization code for token');

      final body = <String, String>{
        'grant_type': 'authorization_code',
        'code': authorizationCode,
        'approval_prompt': 'auto',
        'scope': (scopes ?? _defaultScopes).join(' '),
        'redirect_uri': redirectUri,
        'client_id': clientId,
        'client_secret': clientSecret,
      };

      final response = await http.post(
        Uri.parse(_xboxLiveTokenUrl),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: body.entries.map((e) => '${e.key}=${Uri.encodeComponent(e.value)}').join('&'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        developer.log('XboxAuthService: Successfully obtained OAuth token');
        AppLogger.info('Successfully obtained Xbox OAuth token');
        return data;
      } else {
        developer.log('XboxAuthService: Failed to exchange code: ${response.statusCode} - ${response.body}');
        AppLogger.error('Failed to exchange Xbox authorization code', error: response.body);
        return null;
      }
    } catch (e) {
      developer.log('XboxAuthService: Error exchanging code for token: $e');
      AppLogger.error('Error exchanging Xbox authorization code', error: e);
      return null;
    }
  }

  /// Get Xbox user token
  Future<Map<String, dynamic>?> getUserToken(String accessToken) async {
    try {
      developer.log('XboxAuthService: Getting Xbox user token');
      AppLogger.info('Getting Xbox user token');

      final body = {
        'Properties': {
          'AuthMethod': 'RPS',
          'RpsTicket': 'd=$accessToken',
          'SiteName': 'user.auth.xboxlive.com'
        },
        'RelyingParty': 'http://auth.xboxlive.com',
        'TokenType': 'JWT'
      };

      final response = await http.post(
        Uri.parse(_xboxUserAuthUrl),
        headers: {
          'Content-Type': 'application/json',
          'x-xbl-contract-version': '1',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        developer.log('XboxAuthService: Successfully obtained user token');
        AppLogger.info('Successfully obtained Xbox user token');
        return data;
      } else {
        developer.log('XboxAuthService: Failed to get user token: ${response.statusCode} - ${response.body}');
        AppLogger.error('Failed to get Xbox user token', error: response.body);
        return null;
      }
    } catch (e) {
      developer.log('XboxAuthService: Error getting user token: $e');
      AppLogger.error('Error getting Xbox user token', error: e);
      return null;
    }
  }

  /// Get XSTS token
  Future<Map<String, dynamic>?> getXstsToken(String userToken) async {
    try {
      developer.log('XboxAuthService: Getting XSTS token');
      AppLogger.info('Getting Xbox XSTS token');

      final body = {
        'Properties': {
          'UserTokens': [userToken],
          'SandboxId': 'RETAIL'
        },
        'RelyingParty': 'http://xboxlive.com',
        'TokenType': 'JWT'
      };

      final response = await http.post(
        Uri.parse(_xboxXstsAuthUrl),
        headers: {
          'Content-Type': 'application/json',
          'x-xbl-contract-version': '1',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        developer.log('XboxAuthService: Successfully obtained XSTS token');
        AppLogger.info('Successfully obtained Xbox XSTS token');
        return data;
      } else {
        developer.log('XboxAuthService: Failed to get XSTS token: ${response.statusCode} - ${response.body}');
        AppLogger.error('Failed to get Xbox XSTS token', error: response.body);
        return null;
      }
    } catch (e) {
      developer.log('XboxAuthService: Error getting XSTS token: $e');
      AppLogger.error('Error getting Xbox XSTS token', error: e);
      return null;
    }
  }

  /// Complete Xbox authentication flow and link account
  Future<bool> linkXboxAccount({
    required String accessToken,
    required String userToken,
    required String xstsToken,
    required String userHash,
    required String gamertag,
    required String xuid,
  }) async {
    try {
      developer.log('XboxAuthService: Linking Xbox account');
      AppLogger.info('Linking Xbox account to user');

      final body = {
        'accessToken': accessToken,
        'userToken': userToken,
        'xstsToken': xstsToken,
        'userHash': userHash,
        'gamertag': gamertag,
        'xuid': xuid,
      };

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/xbox/link',
        body: body,
      );

      final data = ApiService.instance.parseResponse(response);
      
      if (response.statusCode == 200) {
        developer.log('XboxAuthService: Successfully linked Xbox account');
        AppLogger.info('Successfully linked Xbox account');
        return true;
      } else {
        developer.log('XboxAuthService: Failed to link Xbox account: ${data['error']}');
        AppLogger.error('Failed to link Xbox account', error: data['error']);
        return false;
      }
    } catch (e) {
      developer.log('XboxAuthService: Error linking Xbox account: $e');
      AppLogger.error('Error linking Xbox account', error: e);
      return false;
    }
  }

  /// Get linked Xbox account information
  Future<Map<String, dynamic>?> getLinkedAccount() async {
    try {
      developer.log('XboxAuthService: Getting linked Xbox account');
      AppLogger.info('Getting linked Xbox account information');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/account',
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        developer.log('XboxAuthService: Successfully retrieved Xbox account info');
        AppLogger.info('Successfully retrieved Xbox account information');
        return data['xboxAccount'] as Map<String, dynamic>?;
      } else if (response.statusCode == 404) {
        developer.log('XboxAuthService: No Xbox account linked');
        AppLogger.info('No Xbox account linked to user');
        return null;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log('XboxAuthService: Failed to get Xbox account: ${data['error']}');
        AppLogger.error('Failed to get Xbox account', error: data['error']);
        return null;
      }
    } catch (e) {
      developer.log('XboxAuthService: Error getting Xbox account: $e');
      AppLogger.error('Error getting Xbox account', error: e);
      return null;
    }
  }

  /// Unlink Xbox account
  Future<bool> unlinkAccount() async {
    try {
      developer.log('XboxAuthService: Unlinking Xbox account');
      AppLogger.info('Unlinking Xbox account from user');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'DELETE',
        path: '/xbox/account',
      );

      if (response.statusCode == 200) {
        developer.log('XboxAuthService: Successfully unlinked Xbox account');
        AppLogger.info('Successfully unlinked Xbox account');
        return true;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log('XboxAuthService: Failed to unlink Xbox account: ${data['error']}');
        AppLogger.error('Failed to unlink Xbox account', error: data['error']);
        return false;
      }
    } catch (e) {
      developer.log('XboxAuthService: Error unlinking Xbox account: $e');
      AppLogger.error('Error unlinking Xbox account', error: e);
      return false;
    }
  }

  /// Extract user information from Xbox tokens
  Map<String, dynamic> extractUserInfo({
    required Map<String, dynamic> userTokenData,
    required Map<String, dynamic> xstsTokenData,
  }) {
    try {
      final userHash = xstsTokenData['DisplayClaims']?['xui']?[0]?['uhs'] as String?;
      final xstsToken = xstsTokenData['Token'] as String?;
      final userToken = userTokenData['Token'] as String?;

      // For gamertag and XUID, we would typically need to make additional API calls
      // to Xbox Live profile endpoints. For now, we'll return what we have.
      
      return {
        'userHash': userHash,
        'xstsToken': xstsToken,
        'userToken': userToken,
        'tokenExpiry': xstsTokenData['NotAfter'],
      };
    } catch (e) {
      developer.log('XboxAuthService: Error extracting user info: $e');
      AppLogger.error('Error extracting Xbox user info', error: e);
      return {};
    }
  }
}
