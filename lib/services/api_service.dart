import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:http/http.dart' as http;
import 'config_service.dart';
import 'aws_auth_service.dart';
import 'newrelic_service.dart';
import '../utils/app_logger.dart';

/// Base API service for communicating with AWS backend
class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();

  ApiService._();

  // Retry configuration
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(seconds: 1);

  /// Get the current server URL from ConfigService
  Future<String> getServerUrl() async {
    return await ConfigService.instance.getServerUrl();
  }

  /// Make an HTTP request to the SAM backend with retry logic
  Future<http.Response> makeRequest({
    required String method,
    required String path,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    String? accessToken,
    Map<String, String>? queryParams,
  }) async {
    return await _makeRequestWithRetry(
      method: method,
      path: path,
      headers: headers,
      body: body,
      accessToken: accessToken,
      queryParams: queryParams,
    );
  }

  /// Make an authenticated HTTP request using the current user's access token
  /// This method automatically handles token refresh if the token is expired
  Future<http.Response> makeAuthenticatedRequest({
    required String method,
    required String path,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
  }) async {
    final accessToken = await AwsAuthService.instance.getValidAccessToken();
    if (accessToken == null) {
      throw ApiException(
        message: 'User not authenticated',
        statusCode: 401,
        details: {'error': 'No access token available'},
      );
    }

    return await makeRequest(
      method: method,
      path: path,
      headers: headers,
      body: body,
      accessToken: accessToken,
      queryParams: queryParams,
    );
  }

  /// Internal method that implements retry logic for network requests
  Future<http.Response> _makeRequestWithRetry({
    required String method,
    required String path,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    String? accessToken,
    Map<String, String>? queryParams,
    int attempt = 1,
  }) async {
    // Track request start time for New Relic (outside try block for error tracking)
    final startTime = DateTime.now().millisecondsSinceEpoch;
    Uri? uri;

    try {
      final serverUrl = await getServerUrl();

      // Ensure path starts with /
      final cleanPath = path.startsWith('/') ? path : '/$path';
      final url = '$serverUrl$cleanPath';
      uri =
          queryParams != null && queryParams.isNotEmpty
              ? Uri.parse(url).replace(queryParameters: queryParams)
              : Uri.parse(url);

      final requestHeaders = <String, String>{
        'Content-Type': 'application/json',
        ...?headers,
      };

      if (accessToken != null) {
        requestHeaders['Authorization'] = 'Bearer $accessToken';
      }

      developer.log('API Request (attempt $attempt): $method $url');
      AppLogger.api(
        'Request (attempt $attempt): $method $url',
        data: {
          'url': url,
          'headers': requestHeaders,
          'body': body,
          'attempt': attempt,
        },
      );

      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: requestHeaders);
          break;
        case 'POST':
          response = await http.post(
            uri,
            headers: requestHeaders,
            body: body != null ? json.encode(body) : null,
          );
          break;
        case 'PUT':
          response = await http.put(
            uri,
            headers: requestHeaders,
            body: body != null ? json.encode(body) : null,
          );
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: requestHeaders);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      // Track successful request in New Relic
      final endTime = DateTime.now().millisecondsSinceEpoch;
      final requestBodySize = body != null ? json.encode(body).length : 0;
      final responseBodySize = response.bodyBytes.length;

      NewRelicService.instance.recordHttpRequest(
        url: uri.toString(),
        method: method.toUpperCase(),
        statusCode: response.statusCode,
        startTime: startTime,
        endTime: endTime,
        bytesSent: requestBodySize,
        bytesReceived: responseBodySize,
        headers: requestHeaders,
      );

      developer.log('API Response (attempt $attempt): ${response.statusCode}');
      AppLogger.api(
        'Response (attempt $attempt): ${response.statusCode}',
        data: {
          'statusCode': response.statusCode,
          'body': response.body,
          'attempt': attempt,
        },
      );

      // Handle 401 authentication errors with automatic token refresh
      if (response.statusCode == 401 && accessToken != null && attempt == 1) {
        developer.log('API Request received 401, attempting token refresh...');
        AppLogger.warning('Received 401 error, attempting token refresh');

        // Try to refresh the token
        final refreshSuccess = await AwsAuthService.instance.refreshToken();
        if (refreshSuccess) {
          developer.log('Token refresh successful, retrying original request');
          AppLogger.info('Token refresh successful, retrying request');

          // Get the new access token and retry the request
          final newAccessToken = AwsAuthService.instance.accessToken;
          if (newAccessToken != null) {
            return await _makeRequestWithRetry(
              method: method,
              path: path,
              headers: headers,
              body: body,
              accessToken: newAccessToken,
              attempt: 2, // Mark as attempt 2 to prevent infinite recursion
            );
          }
        } else {
          developer.log('Token refresh failed, request will fail with 401');
          AppLogger.error('Token refresh failed');
        }
      }

      return response;
    } catch (e) {
      final isNetworkError = _isNetworkError(e);
      final shouldRetry = isNetworkError && attempt < _maxRetries;

      // Track failed request in New Relic
      final endTime = DateTime.now().millisecondsSinceEpoch;
      if (uri != null) {
        NewRelicService.instance.recordNetworkFailure(
          url: uri.toString(),
          method: method.toUpperCase(),
          startTime: startTime,
          endTime: endTime,
          errorMessage: e.toString(),
        );
      }

      developer.log('API Request failed (attempt $attempt): $e');
      AppLogger.error('API Request failed (attempt $attempt)', error: e);

      if (shouldRetry) {
        final delay = Duration(
          milliseconds: _baseDelay.inMilliseconds * attempt,
        );
        developer.log('Retrying request in ${delay.inMilliseconds}ms...');
        AppLogger.warning(
          'Retrying request in ${delay.inMilliseconds}ms...',
          error: e,
        );

        await Future.delayed(delay);

        return await _makeRequestWithRetry(
          method: method,
          path: path,
          headers: headers,
          body: body,
          accessToken: accessToken,
          attempt: attempt + 1,
        );
      }

      // If we've exhausted retries or it's not a network error, rethrow
      rethrow;
    }
  }

  /// Check if an error is a network-related error that should be retried
  bool _isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Common network error patterns
    return errorString.contains('host lookup failed') ||
        errorString.contains('network is unreachable') ||
        errorString.contains('connection refused') ||
        errorString.contains('connection timed out') ||
        errorString.contains('connection reset') ||
        errorString.contains('no route to host') ||
        errorString.contains('temporary failure in name resolution') ||
        errorString.contains('socketexception') ||
        errorString.contains('handshake exception') ||
        (error is SocketException) ||
        (error is HttpException);
  }

  /// Parse JSON response and handle errors
  Map<String, dynamic> parseResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // Handle empty response body
      if (response.body.isEmpty) {
        throw ApiException(
          message: 'Empty response from server',
          statusCode: response.statusCode,
          details: {},
        );
      }
      return json.decode(response.body) as Map<String, dynamic>;
    } else {
      final errorBody =
          response.body.isNotEmpty
              ? json.decode(response.body) as Map<String, dynamic>
              : <String, dynamic>{};

      final errorMessage =
          errorBody['error'] as String? ??
          errorBody['message'] as String? ??
          'HTTP ${response.statusCode}';

      final errorDetails = errorBody['details'] as String? ?? '';

      throw ApiException(
        message: errorMessage,
        statusCode: response.statusCode,
        details: errorBody,
        userFriendlyMessage:
            errorDetails.isNotEmpty ? errorDetails : errorMessage,
      );
    }
  }

  /// Test connectivity to the API endpoint
  /// Returns a map with connectivity status and details
  Future<Map<String, dynamic>> testConnectivity() async {
    try {
      developer.log('Testing API connectivity...');
      AppLogger.info('Testing API connectivity...');

      final response = await makeRequest(method: 'GET', path: '/health');

      final isHealthy =
          response.statusCode == 200 || response.statusCode == 403;
      final status = isHealthy ? 'connected' : 'error';

      final result = {
        'status': status,
        'statusCode': response.statusCode,
        'message':
            isHealthy
                ? 'Successfully connected to API endpoint'
                : 'API endpoint returned error: ${response.statusCode}',
        'timestamp': DateTime.now().toIso8601String(),
        'endpoint': await getServerUrl(),
      };

      developer.log('Connectivity test result: $result');
      AppLogger.info('Connectivity test completed', error: result);

      return result;
    } catch (e) {
      final result = {
        'status': 'failed',
        'message': 'Failed to connect to API: ${e.toString()}',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
        'endpoint': await getServerUrl(),
        'isNetworkError': _isNetworkError(e),
      };

      developer.log('Connectivity test failed: $result');
      AppLogger.error('Connectivity test failed', error: result);

      return result;
    }
  }

  /// Clear cached values (useful for testing or environment changes)
  void clearCache() {
    ConfigService.instance.clearCache();
  }
}

/// Custom exception for API errors
class ApiException implements Exception {
  final String message;
  final int statusCode;
  final Map<String, dynamic> details;
  final String userFriendlyMessage;

  ApiException({
    required this.message,
    required this.statusCode,
    this.details = const {},
    String? userFriendlyMessage,
  }) : userFriendlyMessage = userFriendlyMessage ?? message;

  @override
  String toString() => 'ApiException: $message (HTTP $statusCode)';
}
