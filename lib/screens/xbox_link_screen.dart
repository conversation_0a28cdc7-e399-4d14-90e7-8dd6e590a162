import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/xbox_auth_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_logger.dart';
import '../widgets/common/gf_button.dart';

class XboxLinkScreen extends StatefulWidget {
  const XboxLinkScreen({super.key});

  @override
  State<XboxLinkScreen> createState() => _XboxLinkScreenState();
}

class _XboxLinkScreenState extends State<XboxLinkScreen> {
  bool _isLoading = false;
  String? _errorMessage;

  // These would typically come from your app configuration
  static const String _clientId = 'YOUR_XBOX_CLIENT_ID'; // Replace with actual client ID
  static const String _redirectUri = 'https://your-app.com/xbox/callback'; // Replace with actual redirect URI

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        title: const Text('Link Xbox Account'),
        backgroundColor: AppColors.gfDarkBackground,
        foregroundColor: AppColors.gfOffWhite,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Xbox logo/icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.gfCardBackground,
                borderRadius: BorderRadius.circular(60),
                border: Border.all(
                  color: AppColors.gfGrayBorder,
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.videogame_asset,
                size: 60,
                color: AppColors.gfPrimary,
              ),
            ),
            const SizedBox(height: 32),

            // Title
            Text(
              'Connect Your Xbox Account',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: AppColors.gfOffWhite,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Description
            Text(
              'Link your Xbox account to browse and download your screenshots and game clips directly in GameFlex.',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.gfGrayText,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Features list
            _buildFeaturesList(),
            const SizedBox(height: 32),

            // Error message
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: const TextStyle(
                          color: Colors.red,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Link button
            GFButton(
              text: _isLoading ? 'Connecting...' : 'Link Xbox Account',
              onPressed: _isLoading ? null : _startXboxLinking,
              isLoading: _isLoading,
            ),
            const SizedBox(height: 16),

            // Cancel button
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: AppColors.gfGrayText,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      'Access your Xbox screenshots',
      'Browse your game clips',
      'Download media for editing',
      'Share Xbox content in posts',
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.gfCardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.gfGrayBorder,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'What you can do:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.gfOffWhite,
            ),
          ),
          const SizedBox(height: 12),
          ...features.map((feature) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColors.gfPrimary,
                  size: 16,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    feature,
                    style: TextStyle(
                      color: AppColors.gfGrayText,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Future<void> _startXboxLinking() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Generate Xbox Live authorization URL
      final authUrl = XboxAuthService.instance.generateAuthUrl(
        clientId: _clientId,
        redirectUri: _redirectUri,
        state: 'gameflex_xbox_link', // Optional state parameter
      );

      AppLogger.info('Starting Xbox authentication flow');

      // Launch the authorization URL in the browser
      final uri = Uri.parse(authUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );

        // Show instructions to user
        if (mounted) {
          _showAuthInstructions();
        }
      } else {
        throw Exception('Could not launch Xbox authentication URL');
      }
    } catch (e) {
      AppLogger.error('Error starting Xbox linking', error: e);
      setState(() {
        _errorMessage = 'Failed to start Xbox authentication. Please try again.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showAuthInstructions() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.gfCardBackground,
        title: Text(
          'Complete Xbox Authentication',
          style: TextStyle(color: AppColors.gfOffWhite),
        ),
        content: Text(
          'Please complete the Xbox authentication in your browser, then return to the app.\n\n'
          'Note: This is a demo implementation. In a production app, you would handle the OAuth callback automatically.',
          style: TextStyle(color: AppColors.gfGrayText),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Close Xbox link screen
            },
            child: Text(
              'Done',
              style: TextStyle(color: AppColors.gfPrimary),
            ),
          ),
        ],
      ),
    );
  }
}
