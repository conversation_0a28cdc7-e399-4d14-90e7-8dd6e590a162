import 'package:flutter/material.dart';
import '../models/xbox_media_model.dart';
import '../services/xbox_media_service.dart';
import '../services/xbox_auth_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_logger.dart';
import '../widgets/xbox_media_item_widget.dart';
import '../widgets/common/gf_button.dart';

class XboxMediaBrowserScreen extends StatefulWidget {
  final Function(XboxMediaItem)? onMediaSelected;

  const XboxMediaBrowserScreen({
    super.key,
    this.onMediaSelected,
  });

  @override
  State<XboxMediaBrowserScreen> createState() => _XboxMediaBrowserScreenState();
}

class _XboxMediaBrowserScreenState extends State<XboxMediaBrowserScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<XboxMediaItem> _allMedia = [];
  List<XboxMediaItem> _screenshots = [];
  List<XboxMediaItem> _gameClips = [];
  List<String> _gameFilters = [];
  
  bool _isLoading = true;
  bool _hasXboxAccount = false;
  String? _selectedGameFilter;
  String _searchQuery = '';
  
  XboxAccount? _xboxAccount;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _checkXboxAccountAndLoadMedia();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkXboxAccountAndLoadMedia() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check if Xbox account is linked
      final accountData = await XboxAuthService.instance.getLinkedAccount();
      
      if (accountData != null) {
        _xboxAccount = XboxAccount.fromJson(accountData);
        _hasXboxAccount = true;
        
        if (_xboxAccount!.tokenValid) {
          await _loadMedia();
        } else {
          // Token expired, need to re-authenticate
          AppLogger.info('Xbox token expired, need re-authentication');
        }
      } else {
        _hasXboxAccount = false;
        AppLogger.info('No Xbox account linked');
      }
    } catch (e) {
      AppLogger.error('Error checking Xbox account', error: e);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMedia() async {
    try {
      final media = await XboxMediaService.instance.getAllMedia(
        screenshotLimit: 50,
        gameClipLimit: 25,
      );

      setState(() {
        _allMedia = media;
        _screenshots = media.where((item) => item.isScreenshot).toList();
        _gameClips = media.where((item) => item.isGameClip).toList();
        _gameFilters = XboxMediaService.instance.getUniqueGameTitles(media);
      });

      AppLogger.info('Loaded ${media.length} Xbox media items');
    } catch (e) {
      AppLogger.error('Error loading Xbox media', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load Xbox media'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<XboxMediaItem> _getFilteredMedia(List<XboxMediaItem> media) {
    List<XboxMediaItem> filtered = media;

    // Apply game filter
    if (_selectedGameFilter != null && _selectedGameFilter!.isNotEmpty) {
      filtered = XboxMediaService.instance.filterByGame(filtered, _selectedGameFilter!);
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) =>
        item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        item.gameTitle.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }

    return filtered;
  }

  Widget _buildNoXboxAccountView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.videogame_asset_off,
              size: 80,
              color: AppColors.gfGrayText,
            ),
            const SizedBox(height: 24),
            Text(
              'No Xbox Account Linked',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.gfOffWhite,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Link your Xbox account to browse and download your screenshots and game clips.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.gfGrayText,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 32),
            GFButton(
              text: 'Link Xbox Account',
              onPressed: () {
                // Navigate to Xbox linking screen
                Navigator.of(context).pushNamed('/xbox-link');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTokenExpiredView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.refresh,
              size: 80,
              color: AppColors.gfGrayText,
            ),
            const SizedBox(height: 24),
            Text(
              'Xbox Token Expired',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.gfOffWhite,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Your Xbox authentication has expired. Please re-authenticate to access your media.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.gfGrayText,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 32),
            GFButton(
              text: 'Re-authenticate Xbox',
              onPressed: () {
                // Navigate to Xbox linking screen
                Navigator.of(context).pushNamed('/xbox-link');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search bar
          TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            decoration: InputDecoration(
              hintText: 'Search media...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.gfGrayBorder),
              ),
              filled: true,
              fillColor: AppColors.gfCardBackground,
            ),
            style: TextStyle(color: AppColors.gfOffWhite),
          ),
          const SizedBox(height: 12),
          // Game filter dropdown
          if (_gameFilters.isNotEmpty)
            DropdownButtonFormField<String>(
              value: _selectedGameFilter,
              decoration: InputDecoration(
                labelText: 'Filter by Game',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.gfGrayBorder),
                ),
                filled: true,
                fillColor: AppColors.gfCardBackground,
              ),
              dropdownColor: AppColors.gfCardBackground,
              style: TextStyle(color: AppColors.gfOffWhite),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('All Games'),
                ),
                ..._gameFilters.map((game) => DropdownMenuItem<String>(
                  value: game,
                  child: Text(game),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedGameFilter = value;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildMediaGrid(List<XboxMediaItem> media) {
    final filteredMedia = _getFilteredMedia(media);

    if (filteredMedia.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.photo_library_outlined,
                size: 64,
                color: AppColors.gfGrayText,
              ),
              const SizedBox(height: 16),
              Text(
                'No media found',
                style: TextStyle(
                  fontSize: 18,
                  color: AppColors.gfGrayText,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 16 / 9,
      ),
      itemCount: filteredMedia.length,
      itemBuilder: (context, index) {
        final mediaItem = filteredMedia[index];
        return XboxMediaItemWidget(
          mediaItem: mediaItem,
          onTap: () {
            if (widget.onMediaSelected != null) {
              widget.onMediaSelected!(mediaItem);
              Navigator.of(context).pop();
            }
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        title: const Text('Xbox Media'),
        backgroundColor: AppColors.gfDarkBackground,
        foregroundColor: AppColors.gfOffWhite,
        elevation: 0,
        actions: [
          if (_hasXboxAccount && _xboxAccount?.tokenValid == true)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadMedia,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : !_hasXboxAccount
              ? _buildNoXboxAccountView()
              : _xboxAccount?.tokenValid != true
                  ? _buildTokenExpiredView()
                  : Column(
                      children: [
                        _buildFilterBar(),
                        TabBar(
                          controller: _tabController,
                          labelColor: AppColors.gfOffWhite,
                          unselectedLabelColor: AppColors.gfGrayText,
                          indicatorColor: AppColors.gfPrimary,
                          tabs: [
                            Tab(text: 'All (${_allMedia.length})'),
                            Tab(text: 'Screenshots (${_screenshots.length})'),
                            Tab(text: 'Game Clips (${_gameClips.length})'),
                          ],
                        ),
                        Expanded(
                          child: TabBarView(
                            controller: _tabController,
                            children: [
                              _buildMediaGrid(_allMedia),
                              _buildMediaGrid(_screenshots),
                              _buildMediaGrid(_gameClips),
                            ],
                          ),
                        ),
                      ],
                    ),
    );
  }
}
