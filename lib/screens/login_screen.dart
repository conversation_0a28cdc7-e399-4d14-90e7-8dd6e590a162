import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/common/gf_text_field.dart';
import 'package:gameflex_mobile/widgets/dev_server_dropdown.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/services/api_service.dart';
import 'package:gameflex_mobile/services/config_service.dart';
import 'package:gameflex_mobile/utils/app_logger.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _showEmailForm = false;
  bool _isLogin = false;

  @override
  void initState() {
    super.initState();
    // Auto-fill credentials only in development mode (not staging/production)
    if (ConfigService.instance.isDevelopment) {
      _emailController.text = '<EMAIL>';
      _passwordController.text = 'DevPassword123!';
    }

    // Clear errors when user starts typing
    _emailController.addListener(_clearErrorOnChange);
    _passwordController.addListener(_clearErrorOnChange);
  }

  void _clearErrorOnChange() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.errorMessage != null) {
      authProvider.clearError();
    }
  }

  @override
  void dispose() {
    _emailController.removeListener(_clearErrorOnChange);
    _passwordController.removeListener(_clearErrorOnChange);
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleSignIn() async {
    if (_formKey.currentState?.validate() ?? false) {
      // For sign up, make sure passwords match
      if (!_isLogin &&
          _passwordController.text != _confirmPasswordController.text) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Passwords do not match')));
        return;
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      developer.log(
        'LoginScreen: Starting authentication process - isLogin: $_isLogin',
      );
      AppLogger.auth('Starting authentication process - isLogin: $_isLogin');

      // Clear any previous errors
      authProvider.clearError();

      bool success;
      if (_isLogin) {
        success = await authProvider.signIn(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );
      } else {
        success = await authProvider.signUp(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );
      }

      developer.log('LoginScreen: Authentication result: $success');
      AppLogger.auth('Authentication result: $success');
      if (!success) {
        AppLogger.auth('Error message: ${authProvider.errorMessage}');
      }

      if (mounted) {
        if (success && !_isLogin) {
          // Show success message for signup
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                authProvider.isAuthenticated
                    ? 'Account created and signed in successfully!'
                    : 'Account created successfully! Please sign in.',
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );

          // If auto sign-in failed, switch to login mode
          if (!authProvider.isAuthenticated) {
            setState(() {
              _isLogin = true;
            });
          }
        } else if (!success) {
          // Show error message
          final errorMessage = _formatErrorMessage(authProvider.errorMessage);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 6),
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      }
    }
  }

  /// Handle Apple Sign In
  Future<void> _handleAppleSignIn() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    developer.log('LoginScreen: Starting Apple Sign In');
    AppLogger.auth('Starting Apple Sign In');

    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Signing in with Apple...'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    // Clear any previous errors
    authProvider.clearError();

    final success = await authProvider.signInWithApple();

    developer.log('LoginScreen: Apple Sign In result: $success');
    AppLogger.auth('Apple Sign In result: $success');

    if (mounted) {
      // Hide any existing snackbars
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (success) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Apple Sign In successful!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        // Show error message
        final errorMessage = _formatErrorMessage(authProvider.errorMessage);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 6),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  /// Format error messages to be more user-friendly
  String _formatErrorMessage(String? errorMessage) {
    if (errorMessage == null) return 'Authentication failed. Please try again.';

    final lowerError = errorMessage.toLowerCase();

    // Handle AWS Cognito and API-specific errors
    if (lowerError.contains('invalid email or password')) {
      return 'Invalid email or password. Please check your credentials and try again.';
    } else if (lowerError.contains('email already registered') ||
        lowerError.contains('email already exists')) {
      return 'An account with this email already exists. Try logging in instead.';
    } else if (lowerError.contains('invalid password') ||
        lowerError.contains('password must be at least') ||
        lowerError.contains('password should be at least') ||
        lowerError.contains('weak password')) {
      return 'Password must be at least 8 characters long and contain uppercase, lowercase, and numeric characters.';
    } else if (lowerError.contains('invalid email')) {
      return 'Please enter a valid email address.';
    } else if (lowerError.contains('email not confirmed')) {
      return 'Please check your email and confirm your account before signing in.';
    } else if (lowerError.contains('too many attempts') ||
        lowerError.contains('too many requests')) {
      return 'Too many attempts. Please wait a moment before trying again.';
    } else if (lowerError.contains('network') ||
        lowerError.contains('connection') ||
        lowerError.contains('timeout')) {
      return 'Network error. Please check your connection and try again.';
    } else if (lowerError.contains('apiexception')) {
      // Strip the "ApiException: " prefix if present
      return errorMessage.replaceFirst(
        RegExp(r'^apiexception:\s*', caseSensitive: false),
        '',
      );
    }

    // Return the original message if no specific formatting is needed
    return errorMessage;
  }

  Future<void> _testConnection() async {
    developer.log('LoginScreen: Testing AWS backend connection');
    AppLogger.info('Testing AWS backend connection');

    try {
      // Test AWS backend connection by making a simple API call
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/health',
      );

      final connectionTest = response.statusCode == 200;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              connectionTest
                  ? 'AWS backend connection successful!'
                  : 'AWS backend connection failed - check backend',
            ),
            backgroundColor: connectionTest ? Colors.green : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      developer.log(
        'LoginScreen: AWS backend connection test result: $connectionTest',
      );
      AppLogger.info('AWS backend connection test result: $connectionTest');
    } catch (e, stackTrace) {
      developer.log(
        'LoginScreen: AWS backend connection test error',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('AWS BACKEND CONNECTION TEST ERROR', error: e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection test failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          floatingActionButton:
              ConfigService.instance.isDevelopment
                  ? FloatingActionButton(
                    onPressed: _testConnection,
                    backgroundColor: AppColors.gfGreen,
                    child: const Icon(Icons.wifi, color: Colors.black),
                  )
                  : null,
          floatingActionButtonLocation: FloatingActionButtonLocation.miniEndTop,
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.gfLightTeal,
                      AppColors.gfTeal,
                      AppColors.gfDarkBlue,
                    ],
                    stops: [0.0, 0.5, 1.0],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // GameFlex Logo - Centered in the top half of the screen
                        Expanded(
                          flex:
                              5, // This will take up the top half of the screen
                          child: Center(
                            child: Column(
                              mainAxisAlignment:
                                  MainAxisAlignment.center, // Center vertically
                              mainAxisSize:
                                  MainAxisSize.min, // Use minimum space needed
                              children: [
                                SizedBox(
                                  width:
                                      180, // Slightly smaller to balance with wider logo
                                  height: 180,
                                  child: SvgPicture.asset(
                                    'assets/images/icons/icon_teal.svg',
                                    fit: BoxFit.contain,
                                  ),
                                ),
                                const SizedBox(height: 24),
                                // Logo takes up 80% of screen width (10% margin on each side)
                                LayoutBuilder(
                                  builder: (context, constraints) {
                                    // Calculate 80% of the available width
                                    double logoWidth =
                                        MediaQuery.of(context).size.width * 0.8;
                                    return SizedBox(
                                      width: logoWidth,
                                      child: SvgPicture.asset(
                                        'assets/images/logos/gameflex_text.svg',
                                        fit: BoxFit.fitWidth,
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        // This will take up the bottom half of the screen for buttons
                        Expanded(
                          flex: 8,
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                const SizedBox(height: 20), // Reduced spacing

                                if (!_showEmailForm) ...[
                                  // Sign up with email button
                                  _buildLoginButton(
                                    icon: Icons.email_outlined,
                                    text: 'Sign up with email',
                                    backgroundColor: Colors.black,
                                    textColor: Colors.white,
                                    onPressed: () {
                                      setState(() {
                                        _showEmailForm = true;
                                        _isLogin = false;
                                      });
                                    },
                                  ),
                                ],

                                // Email form that appears when button is clicked
                                if (_showEmailForm) ...[
                                  const SizedBox(height: 10),
                                  // Back button
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: TextButton.icon(
                                      onPressed: () {
                                        setState(() {
                                          _showEmailForm = false;
                                        });
                                      },
                                      icon: const Icon(
                                        Icons.arrow_back,
                                        color: AppColors.gfGreen,
                                      ),
                                      label: const Text(
                                        'Back',
                                        style: TextStyle(
                                          color: AppColors.gfGreen,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Container(
                                    width:
                                        MediaQuery.of(context).size.width * 0.8,
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withValues(
                                        alpha: 179,
                                      ), // 0.7 opacity
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: SingleChildScrollView(
                                      child: Form(
                                        key: _formKey,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.stretch,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            // Development mode indicator and server dropdown
                                            if (ConfigService
                                                .instance
                                                .isDevelopment) ...[
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                  bottom: 12,
                                                ),
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      Icons.bug_report,
                                                      color: AppColors.gfGreen,
                                                      size: 16,
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Expanded(
                                                      child: Text(
                                                        'DEVELOPMENT MODE: Credentials auto-filled',
                                                        style: TextStyle(
                                                          color:
                                                              AppColors.gfGreen,
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    const CompactDevServerDropdown(),
                                                  ],
                                                ),
                                              ),
                                            ],

                                            // Error message display
                                            if (authProvider.errorMessage !=
                                                null) ...[
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                  bottom: 16,
                                                ),
                                                child: Row(
                                                  children: [
                                                    const Icon(
                                                      Icons.error_outline,
                                                      color: Colors.red,
                                                      size: 20,
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Expanded(
                                                      child: Text(
                                                        _formatErrorMessage(
                                                          authProvider
                                                              .errorMessage,
                                                        ),
                                                        style: const TextStyle(
                                                          color: Colors.red,
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ),
                                                    IconButton(
                                                      onPressed: () {
                                                        authProvider
                                                            .clearError();
                                                      },
                                                      icon: const Icon(
                                                        Icons.close,
                                                        color: Colors.red,
                                                        size: 18,
                                                      ),
                                                      constraints:
                                                          const BoxConstraints(
                                                            minWidth: 32,
                                                            minHeight: 32,
                                                          ),
                                                      padding: EdgeInsets.zero,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],

                                            GFTextField(
                                              label: 'Email',
                                              hint: 'Enter your email',
                                              controller: _emailController,
                                              keyboardType:
                                                  TextInputType.emailAddress,
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Please enter your email';
                                                }
                                                if (!value.contains('@')) {
                                                  return 'Please enter a valid email';
                                                }
                                                return null;
                                              },
                                            ),
                                            const SizedBox(height: 12),
                                            GFTextField(
                                              label: 'Password',
                                              hint: 'Enter your password',
                                              controller: _passwordController,
                                              obscureText: !_isPasswordVisible,
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Please enter your password';
                                                }
                                                if (value.length < 6) {
                                                  return 'Password must be at least 6 characters';
                                                }
                                                return null;
                                              },
                                              suffixIcon: IconButton(
                                                icon: Icon(
                                                  _isPasswordVisible
                                                      ? Icons.visibility_off
                                                      : Icons.visibility,
                                                  color: AppColors.gfGrayText,
                                                ),
                                                onPressed: () {
                                                  setState(() {
                                                    _isPasswordVisible =
                                                        !_isPasswordVisible;
                                                  });
                                                },
                                              ),
                                            ),
                                            const SizedBox(height: 12),
                                            // Only show confirm password field for sign up
                                            if (!_isLogin) ...[
                                              GFTextField(
                                                label: 'Confirm Password',
                                                hint: 'Confirm your password',
                                                controller:
                                                    _confirmPasswordController,
                                                obscureText:
                                                    !_isConfirmPasswordVisible,
                                                validator: (value) {
                                                  if (value == null ||
                                                      value.isEmpty) {
                                                    return 'Please confirm your password';
                                                  }
                                                  if (value !=
                                                      _passwordController
                                                          .text) {
                                                    return 'Passwords do not match';
                                                  }
                                                  return null;
                                                },
                                                suffixIcon: IconButton(
                                                  icon: Icon(
                                                    _isConfirmPasswordVisible
                                                        ? Icons.visibility_off
                                                        : Icons.visibility,
                                                    color: AppColors.gfGrayText,
                                                  ),
                                                  onPressed: () {
                                                    setState(() {
                                                      _isConfirmPasswordVisible =
                                                          !_isConfirmPasswordVisible;
                                                    });
                                                  },
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Only show TOS and Sign Up button for sign up (not login)
                                  if (!_isLogin) ...[
                                    const SizedBox(height: 12),
                                    // Terms of Service and Privacy Policy text
                                    Container(
                                      width:
                                          MediaQuery.of(context).size.width *
                                          0.8,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                      ),
                                      child: RichText(
                                        textAlign: TextAlign.center,
                                        text: TextSpan(
                                          style: const TextStyle(
                                            color: AppColors.gfOffWhite,
                                            fontSize: 12,
                                            height: 1.4,
                                          ),
                                          children: [
                                            const TextSpan(
                                              text:
                                                  'By clicking Sign up you acknowledge that you understand the ',
                                            ),
                                            TextSpan(
                                              text: 'Privacy Policy',
                                              style: const TextStyle(
                                                color: AppColors.gfGreen,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                              // In a real app, you would add a GestureRecognizer here
                                              // to handle the link tap
                                            ),
                                            const TextSpan(
                                              text: ' and agree to the ',
                                            ),
                                            TextSpan(
                                              text: 'Terms of Service',
                                              style: const TextStyle(
                                                color: AppColors.gfGreen,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                              // In a real app, you would add a GestureRecognizer here
                                              // to handle the link tap
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    // Large Sign Up button
                                    SizedBox(
                                      width:
                                          MediaQuery.of(context).size.width *
                                          0.8,
                                      height: 55,
                                      child: ElevatedButton(
                                        onPressed:
                                            authProvider.isLoading
                                                ? null
                                                : _handleSignIn,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: const Color(
                                            0xFF28F4C3,
                                          ), // The specified color
                                          foregroundColor: Colors.black,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          elevation: 0,
                                        ),
                                        child:
                                            authProvider.isLoading
                                                ? const Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    SizedBox(
                                                      width: 20,
                                                      height: 20,
                                                      child: CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                        valueColor:
                                                            AlwaysStoppedAnimation<
                                                              Color
                                                            >(Colors.black),
                                                      ),
                                                    ),
                                                    SizedBox(width: 12),
                                                    Text(
                                                      'Signing up...',
                                                      style: TextStyle(
                                                        fontSize: 18,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                  ],
                                                )
                                                : const Text(
                                                  'Sign Up',
                                                  style: TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black,
                                                  ),
                                                ),
                                      ),
                                    ),
                                  ] else ...[
                                    const SizedBox(height: 16),
                                    // Log In button
                                    SizedBox(
                                      width:
                                          MediaQuery.of(context).size.width *
                                          0.8,
                                      height: 55,
                                      child: ElevatedButton(
                                        onPressed:
                                            authProvider.isLoading
                                                ? null
                                                : _handleSignIn,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: const Color(
                                            0xFF28F4C3,
                                          ), // The specified color
                                          foregroundColor: Colors.black,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          elevation: 0,
                                        ),
                                        child:
                                            authProvider.isLoading
                                                ? const Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    SizedBox(
                                                      width: 20,
                                                      height: 20,
                                                      child: CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                        valueColor:
                                                            AlwaysStoppedAnimation<
                                                              Color
                                                            >(Colors.black),
                                                      ),
                                                    ),
                                                    SizedBox(width: 12),
                                                    Text(
                                                      'Logging in...',
                                                      style: TextStyle(
                                                        fontSize: 18,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                  ],
                                                )
                                                : const Text(
                                                  'Log In',
                                                  style: TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black,
                                                  ),
                                                ),
                                      ),
                                    ),
                                  ],
                                ],

                                if (!_showEmailForm) ...[
                                  const SizedBox(height: 20),
                                  // Apple login button
                                  _buildLoginButton(
                                    icon: Icons.apple,
                                    text: 'Log in with Apple ID',
                                    backgroundColor: Colors.white,
                                    textColor: Colors.black,
                                    onPressed: _handleAppleSignIn,
                                  ),
                                  const SizedBox(height: 20),
                                  // Google login button
                                  _buildLoginButton(
                                    icon: Icons.g_mobiledata,
                                    text: 'Log in with Google',
                                    backgroundColor: Colors.white,
                                    textColor: Colors.black,
                                    onPressed: () {
                                      // Google login functionality
                                    },
                                  ),
                                  const SizedBox(height: 24),
                                  // Log in text button
                                  Center(
                                    child: TextButton(
                                      onPressed: () {
                                        setState(() {
                                          _showEmailForm = true;
                                          _isLogin = true;
                                        });
                                      },
                                      child: const Text(
                                        'Log in',
                                        style: TextStyle(
                                          color: AppColors.gfGreen,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Loading overlay - only show when loading and no error
              if (authProvider.isLoading && authProvider.errorMessage == null)
                Container(
                  color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.gfGreen,
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Signing in...',
                          style: TextStyle(
                            color: AppColors.gfOffWhite,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoginButton({
    required IconData icon,
    required String text,
    required Color backgroundColor,
    required Color textColor,
    required VoidCallback onPressed,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate 80% of the available width (10% margin on each side)
        double buttonWidth = MediaQuery.of(context).size.width * 0.8;

        return SizedBox(
          width: buttonWidth,
          height: 55, // Slightly taller buttons
          child: ElevatedButton.icon(
            onPressed: onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor,
              foregroundColor: textColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            icon: Icon(icon, size: 24),
            label: Text(
              text,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: textColor,
              ),
            ),
          ),
        );
      },
    );
  }
}
