import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../providers/auth_provider.dart';
import '../providers/user_profile_provider.dart';
import '../widgets/common/gf_button.dart';
import '../widgets/lazy_image.dart';
import '../models/post_model.dart';

/// User Profile Screen - Full implementation for AWS backend
class UserProfileScreen extends StatefulWidget {
  final String userId;
  final String? username; // Optional, for display in app bar

  const UserProfileScreen({super.key, required this.userId, this.username});

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  late UserProfileProvider _profileProvider;

  @override
  void initState() {
    super.initState();
    _profileProvider = UserProfileProvider();
    _loadProfile();
  }

  @override
  void dispose() {
    _profileProvider.dispose();
    super.dispose();
  }

  void _loadProfile() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _profileProvider.loadUserProfile(widget.userId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final isOwnProfile = authProvider.user?.id == widget.userId;

        return ChangeNotifierProvider.value(
          value: _profileProvider,
          child: Scaffold(
            backgroundColor: AppColors.gfDarkBackground100,
            appBar: AppBar(
              backgroundColor: AppColors.gfDarkBackground100,
              elevation: 0,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
                onPressed: () => Navigator.of(context).pop(),
              ),
              title: Consumer<UserProfileProvider>(
                builder: (context, profileProvider, child) {
                  final displayName =
                      profileProvider.userProfile?['display_name'] as String?;
                  return Text(
                    displayName ?? widget.username ?? 'Profile',
                    style: const TextStyle(
                      color: AppColors.gfOffWhite,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  );
                },
              ),
              actions: [
                if (isOwnProfile)
                  Consumer<UserProfileProvider>(
                    builder: (context, profileProvider, child) {
                      return IconButton(
                        icon:
                            profileProvider.isRefreshing
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.gfOffWhite,
                                    ),
                                  ),
                                )
                                : const Icon(
                                  Icons.refresh,
                                  color: AppColors.gfOffWhite,
                                ),
                        onPressed:
                            profileProvider.isRefreshing
                                ? null
                                : () => _profileProvider.refreshProfile(),
                      );
                    },
                  ),
              ],
            ),
            body: _buildBody(isOwnProfile),
          ),
        );
      },
    );
  }

  Widget _buildBody(bool isOwnProfile) {
    return Consumer<UserProfileProvider>(
      builder: (context, profileProvider, child) {
        if (profileProvider.status == UserProfileStatus.loading) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
            ),
          );
        }

        if (profileProvider.status == UserProfileStatus.error) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppColors.gfOffWhite.withAlpha(128),
                ),
                const SizedBox(height: 16),
                Text(
                  'Error Loading Profile',
                  style: TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  profileProvider.error ?? 'Unknown error occurred',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.gfOffWhite.withAlpha(179),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 24),
                GFButton(
                  text: 'Retry',
                  onPressed:
                      () => profileProvider.loadUserProfile(widget.userId),
                  type: GFButtonType.primary,
                ),
              ],
            ),
          );
        }

        final userProfile = profileProvider.userProfile;
        final userStats = profileProvider.userStats;
        final userPosts = profileProvider.userPosts;

        if (userProfile == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.person_off_outlined,
                  size: 64,
                  color: AppColors.gfOffWhite.withAlpha(128),
                ),
                const SizedBox(height: 16),
                Text(
                  'User Not Found',
                  style: TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'The requested user profile could not be found.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.gfOffWhite.withAlpha(179),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => profileProvider.refreshProfile(),
          color: AppColors.gfGreen,
          backgroundColor: AppColors.gfCardBackground,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProfileHeader(userProfile, userStats, isOwnProfile),
                const SizedBox(height: 24),
                _buildUserPosts(userPosts),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileHeader(
    Map<String, dynamic> userProfile,
    Map<String, dynamic>? userStats,
    bool isOwnProfile,
  ) {
    final displayName =
        userProfile['display_name'] as String? ?? 'Unknown User';
    final username = userProfile['username'] as String? ?? '';
    final bio = userProfile['bio'] as String? ?? '';
    final avatarUrl = userProfile['avatar_url'] as String?;
    final isVerified = userProfile['is_verified'] as bool? ?? false;

    final stats = userStats ?? {};
    final postsCount = stats['posts'] as int? ?? 0;
    final followersCount = stats['followers'] as int? ?? 0;
    final followingCount = stats['following'] as int? ?? 0;
    final likesCount = stats['likes'] as int? ?? 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.gfCardBackground,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.gfGrayBorder, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile picture and basic info
          Row(
            children: [
              // Avatar
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const LinearGradient(
                    colors: [AppColors.gfLightTeal, AppColors.gfTeal],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  border: Border.all(color: AppColors.gfGreen, width: 2),
                ),
                child:
                    avatarUrl != null && avatarUrl.isNotEmpty
                        ? ClipOval(
                          child: LazyImage(
                            imageUrl: avatarUrl,
                            fit: BoxFit.cover,
                            loadOnlyWhenVisible: false,
                            errorWidget: const Icon(
                              Icons.person,
                              size: 40,
                              color: AppColors.gfDarkBlue,
                            ),
                          ),
                        )
                        : const Icon(
                          Icons.person,
                          size: 40,
                          color: AppColors.gfDarkBlue,
                        ),
              ),
              const SizedBox(width: 16),
              // Name and username
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            displayName,
                            style: const TextStyle(
                              color: AppColors.gfOffWhite,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (isVerified) ...[
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.verified,
                            color: AppColors.gfGreen,
                            size: 20,
                          ),
                        ],
                      ],
                    ),
                    if (username.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        '@$username',
                        style: TextStyle(
                          color: AppColors.gfOffWhite.withAlpha(179),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          // Bio
          if (bio.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              bio,
              style: TextStyle(
                color: AppColors.gfOffWhite.withAlpha(230),
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ],

          // Stats
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('Posts', postsCount),
              _buildStatItem('Followers', followersCount),
              _buildStatItem('Following', followingCount),
              _buildStatItem('Likes', likesCount),
            ],
          ),

          // Action buttons
          if (isOwnProfile) ...[
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: GFButton(
                text: 'Edit Profile',
                onPressed: () {
                  // TODO: Navigate to edit profile screen
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Edit profile feature coming soon!'),
                      backgroundColor: AppColors.gfTeal,
                    ),
                  );
                },
                type: GFButtonType.secondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: const TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: AppColors.gfOffWhite.withAlpha(179),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildUserPosts(List<PostModel> posts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Posts (${posts.length})',
          style: const TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (posts.isEmpty)
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.gfCardBackground,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.gfGrayBorder, width: 1),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.post_add_outlined,
                    size: 48,
                    color: AppColors.gfOffWhite.withAlpha(128),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No Posts Yet',
                    style: TextStyle(
                      color: AppColors.gfOffWhite.withAlpha(179),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: posts.length,
            itemBuilder: (context, index) {
              final post = posts[index];
              return _buildPostThumbnail(post);
            },
          ),
      ],
    );
  }

  Widget _buildPostThumbnail(PostModel post) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.gfCardBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.gfGrayBorder, width: 1),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: FutureBuilder<String?>(
          future: post.getMediaUrl(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final mediaUrl = snapshot.data;
            return mediaUrl != null && mediaUrl.isNotEmpty
                ? LazyImage(
                  imageUrl: mediaUrl,
                  fit: BoxFit.cover,
                  loadOnlyWhenVisible:
                      false, // Load immediately for profile thumbnails
                  errorWidget: _buildPostPlaceholder(post),
                )
                : _buildPostPlaceholder(post);
          },
        ),
      ),
    );
  }

  Widget _buildPostPlaceholder(PostModel post) {
    return Container(
      color: AppColors.gfCardBackground,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_outlined,
            color: AppColors.gfOffWhite.withAlpha(128),
            size: 24,
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.all(4),
            child: Text(
              post.content.length > 20
                  ? '${post.content.substring(0, 20)}...'
                  : post.content,
              style: TextStyle(
                color: AppColors.gfOffWhite.withAlpha(179),
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
