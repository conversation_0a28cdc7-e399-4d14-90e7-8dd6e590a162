/// Xbox media types
enum XboxMediaType {
  screenshot,
  gameclip,
}

/// Xbox media item model
class XboxMediaItem {
  final String id;
  final XboxMediaType type;
  final String title;
  final String thumbnailUrl;
  final String downloadUrl;
  final String gameTitle;
  final DateTime dateTaken;
  final int fileSize;
  final int? duration; // For game clips only
  final int resolutionWidth;
  final int resolutionHeight;
  final String platform;
  final String titleId;

  XboxMediaItem({
    required this.id,
    required this.type,
    required this.title,
    required this.thumbnailUrl,
    required this.downloadUrl,
    required this.gameTitle,
    required this.dateTaken,
    required this.fileSize,
    this.duration,
    required this.resolutionWidth,
    required this.resolutionHeight,
    required this.platform,
    required this.titleId,
  });

  /// Create XboxMediaItem from JSON
  factory XboxMediaItem.fromJson(Map<String, dynamic> json) {
    return XboxMediaItem(
      id: json['id'] as String,
      type: _parseMediaType(json['type'] as String),
      title: json['title'] as String? ?? '',
      thumbnailUrl: json['thumbnailUrl'] as String? ?? '',
      downloadUrl: json['downloadUrl'] as String? ?? '',
      gameTitle: json['gameTitle'] as String? ?? 'Unknown Game',
      dateTaken: DateTime.parse(json['dateTaken'] as String),
      fileSize: json['fileSize'] as int? ?? 0,
      duration: json['duration'] as int?,
      resolutionWidth: json['resolutionWidth'] as int? ?? 1920,
      resolutionHeight: json['resolutionHeight'] as int? ?? 1080,
      platform: json['platform'] as String? ?? 'Xbox',
      titleId: json['titleId'] as String? ?? '',
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'thumbnailUrl': thumbnailUrl,
      'downloadUrl': downloadUrl,
      'gameTitle': gameTitle,
      'dateTaken': dateTaken.toIso8601String(),
      'fileSize': fileSize,
      'duration': duration,
      'resolutionWidth': resolutionWidth,
      'resolutionHeight': resolutionHeight,
      'platform': platform,
      'titleId': titleId,
    };
  }

  /// Parse media type from string
  static XboxMediaType _parseMediaType(String typeString) {
    switch (typeString.toLowerCase()) {
      case 'screenshot':
        return XboxMediaType.screenshot;
      case 'gameclip':
        return XboxMediaType.gameclip;
      default:
        return XboxMediaType.screenshot;
    }
  }

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '${fileSize}B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// Get formatted duration for game clips
  String get formattedDuration {
    if (duration == null) return '';
    
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Get resolution string
  String get resolutionString {
    return '${resolutionWidth}x$resolutionHeight';
  }

  /// Check if this is a screenshot
  bool get isScreenshot => type == XboxMediaType.screenshot;

  /// Check if this is a game clip
  bool get isGameClip => type == XboxMediaType.gameclip;

  @override
  String toString() {
    return 'XboxMediaItem(id: $id, type: ${type.name}, title: $title, gameTitle: $gameTitle)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is XboxMediaItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Xbox media response with pagination
class XboxMediaResponse {
  final List<XboxMediaItem> items;
  final bool hasMore;
  final String? contToken;

  XboxMediaResponse({
    required this.items,
    required this.hasMore,
    this.contToken,
  });

  /// Create XboxMediaResponse from JSON
  factory XboxMediaResponse.fromJson(Map<String, dynamic> json) {
    final itemsList = json['items'] as List<dynamic>? ?? [];
    final items = itemsList
        .map((item) => XboxMediaItem.fromJson(item as Map<String, dynamic>))
        .toList();

    return XboxMediaResponse(
      items: items,
      hasMore: json['hasMore'] as bool? ?? false,
      contToken: json['contToken'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'hasMore': hasMore,
      'contToken': contToken,
    };
  }

  @override
  String toString() {
    return 'XboxMediaResponse(items: ${items.length}, hasMore: $hasMore)';
  }
}

/// Xbox account information
class XboxAccount {
  final String gamertag;
  final String xboxUserId;
  final bool isActive;
  final bool tokenValid;
  final DateTime? tokenExpiry;

  XboxAccount({
    required this.gamertag,
    required this.xboxUserId,
    required this.isActive,
    required this.tokenValid,
    this.tokenExpiry,
  });

  /// Create XboxAccount from JSON
  factory XboxAccount.fromJson(Map<String, dynamic> json) {
    return XboxAccount(
      gamertag: json['gamertag'] as String,
      xboxUserId: json['xboxUserId'] as String,
      isActive: json['isActive'] as bool? ?? false,
      tokenValid: json['tokenValid'] as bool? ?? false,
      tokenExpiry: json['tokenExpiry'] != null 
          ? DateTime.parse(json['tokenExpiry'] as String)
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'gamertag': gamertag,
      'xboxUserId': xboxUserId,
      'isActive': isActive,
      'tokenValid': tokenValid,
      'tokenExpiry': tokenExpiry?.toIso8601String(),
    };
  }

  /// Check if token needs refresh
  bool get needsTokenRefresh {
    if (tokenExpiry == null) return true;
    return DateTime.now().isAfter(tokenExpiry!.subtract(const Duration(hours: 1)));
  }

  @override
  String toString() {
    return 'XboxAccount(gamertag: $gamertag, xboxUserId: $xboxUserId, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is XboxAccount && other.xboxUserId == xboxUserId;
  }

  @override
  int get hashCode => xboxUserId.hashCode;
}
