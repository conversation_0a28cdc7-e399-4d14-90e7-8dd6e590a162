import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import '../utils/app_logger.dart';
import '../theme/app_theme.dart';

/// A lazy loading image widget with caching and visibility-based loading
class LazyImage extends StatefulWidget {
  final String imageUrl;
  final BoxFit fit;
  final double? width;
  final double? height;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;
  final Duration? cacheMaxAge;
  final bool loadOnlyWhenVisible;
  final VoidCallback? onImageLoaded;
  final VoidCallback? onImageError;

  const LazyImage({
    super.key,
    required this.imageUrl,
    this.fit = BoxFit.cover,
    this.width,
    this.height,
    this.placeholder,
    this.errorWidget,
    this.enableMemoryCache = true,
    this.cacheMaxAge,
    this.loadOnlyWhenVisible = true,
    this.onImageLoaded,
    this.onImageError,
  });

  @override
  State<LazyImage> createState() => _LazyImageState();
}

class _LazyImageState extends State<LazyImage> {
  bool _isVisible = false;
  bool _hasStartedLoading = false;
  late CacheManager _cacheManager;

  @override
  void initState() {
    super.initState();
    _initializeCacheManager();

    if (!widget.loadOnlyWhenVisible) {
      _startLoading();
    }
  }

  void _initializeCacheManager() {
    _cacheManager = CacheManager(
      Config(
        'lazy_image_cache',
        stalePeriod: widget.cacheMaxAge ?? const Duration(days: 7),
        maxNrOfCacheObjects: 200,
        repo: JsonCacheInfoRepository(databaseName: 'lazy_image_cache'),
        fileService: HttpFileService(),
      ),
    );
  }

  void _startLoading() {
    if (!_hasStartedLoading) {
      setState(() {
        _hasStartedLoading = true;
      });
      AppLogger.debug('LazyImage: Started loading ${widget.imageUrl}');
    }
  }

  void _onVisibilityChanged(bool isVisible) {
    if (isVisible && !_isVisible && widget.loadOnlyWhenVisible) {
      _startLoading();
    }
    _isVisible = isVisible;
  }

  Widget _buildPlaceholder() {
    return widget.placeholder ??
        Container(
          width: widget.width,
          height: widget.height,
          color: AppColors.gfCardBackground,
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              strokeWidth: 2,
            ),
          ),
        );
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget ??
        Container(
          width: widget.width,
          height: widget.height,
          color: AppColors.gfCardBackground,
          child: const Center(
            child: Icon(
              Icons.error_outline,
              color: AppColors.gfGrayText,
              size: 32,
            ),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.loadOnlyWhenVisible) {
      return VisibilityDetector(
        key: Key('lazy_image_${widget.imageUrl}'),
        onVisibilityChanged: (info) {
          _onVisibilityChanged(info.visibleFraction > 0.1);
        },
        child: _buildImageContent(),
      );
    }

    return _buildImageContent();
  }

  Widget _buildImageContent() {
    if (!_hasStartedLoading) {
      return _buildPlaceholder();
    }

    return CachedNetworkImage(
      imageUrl: widget.imageUrl,
      cacheManager: widget.enableMemoryCache ? _cacheManager : null,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) {
        AppLogger.error('LazyImage: Failed to load image', error: error);
        widget.onImageError?.call();
        return _buildErrorWidget();
      },
      imageBuilder: (context, imageProvider) {
        widget.onImageLoaded?.call();
        return Image(
          image: imageProvider,
          width: widget.width,
          height: widget.height,
          fit: widget.fit,
        );
      },
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 100),
    );
  }
}

/// A visibility detector widget for lazy loading
class VisibilityDetector extends StatefulWidget {
  final Widget child;
  final Function(VisibilityInfo) onVisibilityChanged;

  const VisibilityDetector({
    super.key,
    required this.child,
    required this.onVisibilityChanged,
  });

  @override
  State<VisibilityDetector> createState() => _VisibilityDetectorState();
}

class _VisibilityDetectorState extends State<VisibilityDetector> {
  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        _checkVisibility();
        return false;
      },
      child: widget.child,
    );
  }

  void _checkVisibility() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final size = renderBox.size;
      final position = renderBox.localToGlobal(Offset.zero);
      final screenSize = MediaQuery.of(context).size;

      // Calculate visible fraction
      final visibleTop = position.dy < 0 ? 0 : position.dy;
      final visibleBottom =
          position.dy + size.height > screenSize.height
              ? screenSize.height
              : position.dy + size.height;

      final visibleHeight = visibleBottom - visibleTop;
      final visibleFraction =
          visibleHeight > 0 ? visibleHeight / size.height : 0.0;

      widget.onVisibilityChanged(
        VisibilityInfo(
          key: widget.key ?? const ValueKey('visibility_detector'),
          size: size,
          visibleFraction: visibleFraction.clamp(0.0, 1.0),
        ),
      );
    });
  }
}

/// Information about widget visibility
class VisibilityInfo {
  final Key key;
  final Size size;
  final double visibleFraction;

  const VisibilityInfo({
    required this.key,
    required this.size,
    required this.visibleFraction,
  });
}
