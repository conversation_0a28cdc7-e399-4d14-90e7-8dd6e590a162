import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/posts_provider.dart';
import '../providers/auth_provider.dart';
import '../models/post_model.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../services/media_preloader.dart';
import 'feed_item.dart';
import 'feed_top_overlay.dart';
import 'feed_bottom_overlay.dart';

class Feed extends StatefulWidget {
  final Function(PostModel)? onNavigateToReflexes;
  final Function(PostModel)? onCurrentPostChanged;

  const Feed({super.key, this.onNavigateToReflexes, this.onCurrentPostChanged});

  @override
  State<Feed> createState() => _FeedState();
}

class _FeedState extends State<Feed> with AutomaticKeepAliveClientMixin {
  final PageController _pageController = PageController();
  int _currentIndex = 0;
  double _panStartX = 0.0;
  double _panCurrentX = 0.0;
  bool _isNavigatingToReflexes = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Load posts when the widget is initialized, but only if authenticated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);

      if (authProvider.isAuthenticated) {
        if (postsProvider.posts.isEmpty) {
          postsProvider.loadPosts();
        } else {
          // Restore scroll position if posts are already loaded
          _restoreScrollPosition(postsProvider);
        }
      }
    });
  }

  void _restoreScrollPosition(PostsProvider postsProvider) {
    final savedIndex = postsProvider.currentPostIndex;
    if (savedIndex > 0 && savedIndex < postsProvider.posts.length) {
      _currentIndex = savedIndex;
      // Use a small delay to ensure the PageView is built
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted && _pageController.hasClients) {
          _pageController.animateToPage(
            savedIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Consumer2<PostsProvider, AuthProvider>(
      builder: (context, postsProvider, authProvider, child) {
        // If user is not authenticated, show a message
        if (!authProvider.isAuthenticated) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.lock_outline, color: Colors.white, size: 64),
                  SizedBox(height: 16),
                  Text(
                    'Please sign in to view posts',
                    style: TextStyle(color: Colors.white, fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        // If authenticated but posts haven't been loaded yet, try to load them
        if (authProvider.isAuthenticated &&
            postsProvider.status == PostsStatus.initial) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            postsProvider.loadPosts();
          });
        }
        if (postsProvider.status == PostsStatus.loading &&
            postsProvider.posts.isEmpty) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            ),
          );
        }

        if (postsProvider.status == PostsStatus.error) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 64,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading posts',
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(color: Colors.white),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    postsProvider.errorMessage ?? 'Unknown error occurred',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed:
                        authProvider.isAuthenticated
                            ? () => postsProvider.loadPosts()
                            : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.gfGreen,
                      foregroundColor: Colors.black,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        if (postsProvider.posts.isEmpty) {
          return const Scaffold(
            body: Center(
              child: Text(
                'No posts available',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ),
          );
        }

        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Stack(
            children: [
              // Main content - PageView with media only
              GestureDetector(
                onPanStart: (details) {
                  _panStartX = details.globalPosition.dx;
                  _panCurrentX = details.globalPosition.dx;
                  _isNavigatingToReflexes = false; // Reset flag on new gesture
                },
                onPanUpdate: (details) {
                  _panCurrentX = details.globalPosition.dx;
                  final deltaX = _panCurrentX - _panStartX;

                  // Check for significant horizontal movement (swipe left for reflexes)
                  if (deltaX < -80 && !_isNavigatingToReflexes) {
                    // 80 pixels threshold for immediate response
                    _isNavigatingToReflexes = true;
                    _navigateToReflexes();
                    return;
                  }
                },
                onPanEnd: (details) {
                  final velocityX = details.velocity.pixelsPerSecond.dx;
                  final velocityY = details.velocity.pixelsPerSecond.dy;
                  final deltaX = _panCurrentX - _panStartX;

                  AppLogger.debug(
                    'Feed: Pan ended - velocityX: $velocityX, velocityY: $velocityY, deltaX: $deltaX',
                  );

                  // Check for horizontal swipe left (reflexes) - lower threshold for better responsiveness
                  if (!_isNavigatingToReflexes &&
                      ((velocityX < -200 && velocityY.abs() < 400) ||
                          deltaX < -60)) {
                    // Swipe left - open reflexes for current post
                    _isNavigatingToReflexes = true;
                    _navigateToReflexes();
                    return;
                  }

                  // Reset the navigation flag
                  _isNavigatingToReflexes = false;

                  // Vertical scrolling logic
                  if (velocityY > 500) {
                    // Fast swipe down - go to previous page
                    if (_currentIndex > 0) {
                      _pageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  } else if (velocityY < -500) {
                    // Fast swipe up - go to next page
                    if (_currentIndex < postsProvider.posts.length - 1) {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  }
                },
                child: PageView.builder(
                  controller: _pageController,
                  scrollDirection: Axis.vertical,
                  itemCount: postsProvider.posts.length,
                  physics: const ClampingScrollPhysics(),
                  onPageChanged: (index) {
                    AppLogger.debug('Feed: Page changed to index $index');
                    setState(() {
                      _currentIndex = index;
                    });

                    // Save current index to provider for state persistence
                    postsProvider.setCurrentPostIndex(index);

                    // Notify parent about current post change
                    if (widget.onCurrentPostChanged != null &&
                        index < postsProvider.posts.length) {
                      widget.onCurrentPostChanged!(postsProvider.posts[index]);
                    }

                    // Preload upcoming media content
                    MediaPreloader.instance.preloadUpcomingMedia(
                      posts: postsProvider.posts,
                      currentIndex: index,
                      context: context,
                    );

                    // Load more posts when approaching the end
                    if (index >= postsProvider.posts.length - 2 &&
                        postsProvider.hasMore &&
                        postsProvider.status != PostsStatus.loading) {
                      postsProvider.loadMorePosts();
                    }
                  },
                  itemBuilder: (context, index) {
                    if (index >= postsProvider.posts.length) {
                      return const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.gfGreen,
                          ),
                        ),
                      );
                    }

                    final post = postsProvider.posts[index];
                    return FeedItem(
                      post: post,
                      isVisible: index == _currentIndex,
                    );
                  },
                ),
              ),

              // Fixed overlays that update based on current post
              if (postsProvider.posts.isNotEmpty &&
                  _currentIndex < postsProvider.posts.length) ...[
                // Top overlay - User info
                FeedTopOverlay(post: postsProvider.posts[_currentIndex]),

                // Bottom overlay - Action buttons
                FeedBottomOverlay(post: postsProvider.posts[_currentIndex]),
              ],
            ],
          ),
        );
      },
    );
  }

  void _navigateToReflexes() {
    final postsProvider = Provider.of<PostsProvider>(context, listen: false);

    // Get the current post if available
    if (postsProvider.posts.isNotEmpty &&
        _currentIndex < postsProvider.posts.length) {
      final currentPost = postsProvider.posts[_currentIndex];

      // Use callback if provided, otherwise show error
      if (widget.onNavigateToReflexes != null) {
        widget.onNavigateToReflexes!(currentPost);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reflex navigation not available'),
            backgroundColor: Colors.red,
          ),
        );
      }

      _isNavigatingToReflexes = false;
    } else {
      // Show a message if no post is available
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No post available to view reflexes'),
          backgroundColor: Colors.red,
        ),
      );
      _isNavigatingToReflexes = false;
    }
  }
}
