import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class EmojiPickerDialog extends StatelessWidget {
  final void Function(String emoji) onEmojiSelected;
  final String? currentReaction;

  const EmojiPickerDialog({
    super.key,
    required this.onEmojiSelected,
    this.currentReaction,
  });

  // Curated emoji whitelist - basic reactions, extended reactions, and emotions
  static const List<String> _emojis = [
    // Basic reactions
    '👍', '👎', '❤️', '😂', '😮', '😢', '😡', '🔥',
    // Extended reactions
    '👏', '🎉', '💯', '✨', '⚡', '💪', '🙌', '👀',
    // Emotions
    '🤔', '😍', '🥰', '😊', '😎', '🤩', '😴', '🤯',
    '😭', '🥺', '😤', '🤗', '😱', '🤪', '😇', '🙄',
  ];

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.gfDarkBlue,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.gfGreen, width: 1),
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'React with emoji',
                    style: TextStyle(
                      color: AppColors.gfOffWhite,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: AppColors.gfOffWhite),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (currentReaction != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.gfTeal.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Text(
                        'Current: $currentReaction',
                        style: const TextStyle(
                          color: AppColors.gfOffWhite,
                          fontSize: 16,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          onEmojiSelected(''); // Remove reaction
                        },
                        child: const Text(
                          'Remove',
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
              Flexible(
                child: GridView.builder(
                  shrinkWrap: true,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 6,
                    mainAxisSpacing: 8,
                    crossAxisSpacing: 8,
                  ),
                  itemCount: _emojis.length,
                  itemBuilder: (context, index) {
                    final emoji = _emojis[index];
                    final isSelected = emoji == currentReaction;

                    return GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        onEmojiSelected(emoji);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? AppColors.gfGreen.withValues(alpha: 0.3)
                                  : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                          border:
                              isSelected
                                  ? Border.all(
                                    color: AppColors.gfGreen,
                                    width: 2,
                                  )
                                  : null,
                        ),
                        child: Center(
                          child: Text(
                            emoji,
                            style: const TextStyle(fontSize: 24),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
