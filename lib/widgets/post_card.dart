import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../providers/posts_provider.dart';
import '../services/config_service.dart';
import '../services/aws_posts_service.dart';
import '../theme/app_theme.dart';
import '../screens/post_detail_screen.dart';
import 'reaction_bar.dart';
import 'emoji_picker_dialog.dart';
import 'video_player_widget.dart';
import 'lazy_image.dart';

class PostCard extends StatefulWidget {
  final PostModel post;
  final VoidCallback? onTap;
  final bool showFullContent;

  const PostCard({
    super.key,
    required this.post,
    this.onTap,
    this.showFullContent = false,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppColors.gfDarkBackground,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: widget.onTap,
        onLongPress: () => _showEmojiPicker(context),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info header
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  _buildAvatar(),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.post.authorDisplayName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppColors.gfOffWhite,
                            fontSize: 16,
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              widget.post.authorUsername,
                              style: const TextStyle(
                                color: AppColors.gfGrayText,
                                fontSize: 12,
                              ),
                            ),
                            const Text(
                              ' • ',
                              style: TextStyle(
                                color: AppColors.gfGrayText,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              widget.post.timeAgo,
                              style: const TextStyle(
                                color: AppColors.gfGrayText,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.more_vert,
                      color: AppColors.gfGrayText,
                    ),
                    onPressed: () => _showPostOptions(context),
                  ),
                ],
              ),
            ),

            // Post content
            if (widget.post.content.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: Text(
                  widget.post.content,
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 14,
                    height: 1.4,
                  ),
                  maxLines: widget.showFullContent ? null : 6,
                  overflow:
                      widget.showFullContent
                          ? TextOverflow.visible
                          : TextOverflow.ellipsis,
                ),
              ),

            // Media content
            if (widget.post.hasMedia) _buildMediaContent(),

            // Reactions section
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 8, 12, 0),
              child: ReactionBar(
                reactions: widget.post.reactions,
                currentUserReaction: widget.post.currentUserReaction,
                onReact: (emoji) => _handleReact(context, emoji),
                onShowReactionPicker: () => _showEmojiPicker(context),
              ),
            ),

            // Action buttons
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                children: [
                  // Debug info (remove in production)
                  if (ConfigService.instance.isDevelopment)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text(
                        'DEBUG: Liked: ${widget.post.isLikedByCurrentUser}, Count: ${widget.post.likeCount}',
                        style: const TextStyle(
                          color: AppColors.gfGrayText,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildActionButton(
                        icon: Icons.shield_outlined,
                        activeIcon: Icons.shield,
                        label: widget.post.likeCount.toString(),
                        isActive: widget.post.isLikedByCurrentUser,
                        onPressed: () => _handleLike(context),
                      ),
                      _buildActionButton(
                        icon: Icons.chat_bubble_outline,
                        label: widget.post.commentCount.toString(),
                        onPressed: () => _handleComment(context),
                      ),
                      _buildActionButton(
                        icon: Icons.share,
                        label: 'Share',
                        onPressed: () => _handleShare(context),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    if (widget.post.avatarUrl != null && widget.post.avatarUrl!.isNotEmpty) {
      return CircleAvatar(
        backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
        radius: 20,
        backgroundImage: NetworkImage(widget.post.avatarUrl!),
        onBackgroundImageError: (_, __) {},
        child:
            widget.post.avatarUrl!.isEmpty
                ? Text(
                  widget.post.authorDisplayName.isNotEmpty
                      ? widget.post.authorDisplayName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: AppColors.gfDarkBlue,
                    fontWeight: FontWeight.bold,
                  ),
                )
                : null,
      );
    }

    return CircleAvatar(
      backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
      radius: 20,
      child: Text(
        widget.post.authorDisplayName.isNotEmpty
            ? widget.post.authorDisplayName[0].toUpperCase()
            : 'U',
        style: const TextStyle(
          color: AppColors.gfDarkBlue,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildMediaContent() {
    if (widget.post.isImage) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        height: 250,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: FutureBuilder<String?>(
            future: widget.post.getMediaUrl(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              final mediaUrl = snapshot.data;
              return mediaUrl != null
                  ? LazyImage(
                    imageUrl: mediaUrl,
                    fit: BoxFit.cover,
                    loadOnlyWhenVisible:
                        false, // Load immediately for post cards
                    errorWidget: _buildMediaPlaceholder('Image'),
                  )
                  : _buildMediaPlaceholder('Image');
            },
          ),
        ),
      );
    } else if (widget.post.isVideo) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        height: 250,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: FutureBuilder<String?>(
            future: widget.post.getMediaUrl(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              final mediaUrl = snapshot.data;
              return mediaUrl != null
                  ? VideoPlayerWidget(
                    videoUrl: mediaUrl,
                    autoPlay: false, // Don't auto-play in post cards
                    isVisible: true,
                  )
                  : _buildMediaPlaceholder('Video');
            },
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 48,
              color: AppColors.gfGrayText,
            ),
            const SizedBox(height: 8),
            Text(
              '$type Content',
              style: const TextStyle(color: AppColors.gfGrayText, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    IconData? activeIcon,
    required String label,
    bool isActive = false,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isActive && activeIcon != null ? activeIcon : icon,
              color: isActive ? AppColors.gfGreen : AppColors.gfGrayText,
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isActive ? AppColors.gfGreen : AppColors.gfGrayText,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleLike(BuildContext context) async {
    final postsProvider = Provider.of<PostsProvider>(context, listen: false);

    // Show immediate visual feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.post.isLikedByCurrentUser ? 'Unliked post' : 'Liked post',
          style: const TextStyle(color: AppColors.gfOffWhite),
        ),
        backgroundColor: AppColors.gfTeal,
        duration: const Duration(milliseconds: 1500),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );

    // Trigger the like toggle
    await postsProvider.toggleLike(widget.post.id);
  }

  void _handleComment(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: widget.post),
      ),
    );
  }

  void _handleShare(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.gfDarkBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(
                    Icons.bookmark_border,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Save Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Save feature coming soon!'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.report_outlined,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Report Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Report feature coming soon!'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _handleReact(BuildContext context, String emoji) async {
    final postsProvider = Provider.of<PostsProvider>(context, listen: false);
    final idx = postsProvider.posts.indexWhere((p) => p.id == widget.post.id);

    if (idx != -1) {
      final old = postsProvider.posts[idx];
      final newReactions = Map<String, int>.from(old.reactions);

      // Remove current reaction if exists
      if (old.currentUserReaction != null) {
        final currentEmoji = old.currentUserReaction!;
        final currentCount = newReactions[currentEmoji] ?? 0;
        if (currentCount > 1) {
          newReactions[currentEmoji] = currentCount - 1;
        } else {
          newReactions.remove(currentEmoji);
        }
      }

      // Add new reaction (or remove if same emoji)
      String? newUserReaction;
      if (emoji.isNotEmpty && emoji != old.currentUserReaction) {
        newReactions[emoji] = (newReactions[emoji] ?? 0) + 1;
        newUserReaction = emoji;
      }

      postsProvider.updatePost(
        old.copyWith(
          reactions: newReactions,
          currentUserReaction: newUserReaction,
        ),
      );
    }

    // Make API call
    final ok =
        emoji.isEmpty || emoji == widget.post.currentUserReaction
            ? await AwsPostsService.instance.unreactToPost(
              widget.post.id,
              widget.post.currentUserReaction ?? '',
            )
            : await AwsPostsService.instance.reactToPost(widget.post.id, emoji);

    if (!ok && context.mounted) {
      postsProvider.refreshPost(widget.post.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to update reaction'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showEmojiPicker(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => EmojiPickerDialog(
            currentReaction: widget.post.currentUserReaction,
            onEmojiSelected: (emoji) => _handleReact(context, emoji),
          ),
    );
  }
}
