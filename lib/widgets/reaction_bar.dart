import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Discord-style reaction display - only shows reactions that have been used
class ReactionBar extends StatelessWidget {
  final Map<String, int> reactions;
  final String? currentUserReaction; // Single reaction per user
  final void Function(String emoji) onReact;
  final VoidCallback? onShowReactionPicker;
  final VoidCallback? onShowReactionDetails;

  const ReactionBar({
    super.key,
    required this.reactions,
    this.currentUserReaction,
    required this.onReact,
    this.onShowReactionPicker,
    this.onShowReactionDetails,
  });

  @override
  Widget build(BuildContext context) {
    // Only show reactions that have been used (count > 0)
    final activeReactions =
        reactions.entries.where((entry) => entry.value > 0).toList();

    // Always show something - either reactions or just the add button
    return Wrap(
      spacing: 6,
      runSpacing: 4,
      children: [
        ...activeReactions.map(
          (entry) => _ReactionChip(
            emoji: entry.key,
            count: entry.value,
            isSelected: currentUserReaction == entry.key,
            onTap: () => onReact(entry.key),
          ),
        ),
        if (onShowReactionPicker != null)
          _AddReactionButton(
            onTap: onShowReactionPicker!,
            showLabel: activeReactions.isEmpty,
          ),
      ],
    );
  }
}

class _ReactionChip extends StatelessWidget {
  final String emoji;
  final int count;
  final bool isSelected;
  final VoidCallback onTap;

  const _ReactionChip({
    required this.emoji,
    required this.count,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected ? AppColors.gfGreen : Colors.white24,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(emoji, style: const TextStyle(fontSize: 16)),
              if (count > 1) ...[
                const SizedBox(width: 4),
                Text(
                  count.toString(),
                  style: TextStyle(
                    color:
                        isSelected ? AppColors.gfGreen : AppColors.gfOffWhite,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class _AddReactionButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool showLabel;

  const _AddReactionButton({required this.onTap, this.showLabel = false});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.4),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white24, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.add_reaction_outlined,
              size: 16,
              color: AppColors.gfOffWhite,
            ),
            if (showLabel) ...[
              const SizedBox(width: 4),
              const Text(
                'React',
                style: TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
